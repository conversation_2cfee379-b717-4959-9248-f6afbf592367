package com.feidi.xx.cross.power.dubbo;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.feidi.xx.common.core.domain.model.LoginUser;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.domain.PowAgent;
import com.feidi.xx.cross.power.domain.PowAgentUser;
import com.feidi.xx.cross.power.domain.vo.PowAgentVo;
import com.feidi.xx.cross.power.mapper.PowAgentMapper;
import com.feidi.xx.cross.power.service.IPowAgentUserService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 代理商服务接口实现类
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteAgentServiceImpl implements RemoteAgentService {

    private final PowAgentMapper baseMapper;
    private final IPowAgentUserService powAgentUserService;
    @Override
    public RemoteAgentVo getAgentInfoById(Long agentId) {
        PowAgent agent = baseMapper.selectById(agentId);
        if (ObjectUtils.isNull(agent)) {
            return null;
        }
        return BeanUtils.copyProperties(agent, RemoteAgentVo.class);
    }

    @Override
    public List<RemoteAgentVo> getAgentInfoById(List<Long> agentIds) {
        if (CollUtils.isEmpty(agentIds)) {
            return Collections.emptyList();
        }
        List<PowAgentVo> ptAgentVos = baseMapper.selectVoBatchIds(agentIds);
        if (ObjectUtils.isNull(ptAgentVos)) {
            return Collections.emptyList();
        }
        return BeanUtils.copyToList(ptAgentVos, RemoteAgentVo.class);
    }

    @Override
    public List<RemoteAgentVo> getChildAgentInfoById(Long agentId) {
        List<PowAgent> agents = baseMapper.listByParentId(agentId);
        return BeanUtils.copyToList(agents, RemoteAgentVo.class);    }

    @Override
    public LoginUser getAgentInfo(String username, String tenantId) {
        PowAgentUser agentInfo = powAgentUserService.queryByPhone(username);
        if ( agentInfo == null ) {
            return null;
        }

        String status = agentInfo.getStatus();
        if ( !status.equals(UserStatusEnum.OK.getCode()) ) {
            throw new ServiceException("账号已被禁用！");
        }

        LoginUser loginUser = new LoginUser();
        loginUser.setTenantId(tenantId);
        loginUser.setUserId(agentInfo.getId());
        loginUser.setNickname(agentInfo.getName());
        loginUser.setUsername(agentInfo.getPhone());
        loginUser.setUserType(UserTypeEnum.AGENT_USER.getUserType());
        loginUser.setPassword(agentInfo.getPassword());
        loginUser.setAgentId(agentInfo.getAgentId());
        return loginUser;    }

    @Override
    public List<RemoteAgentVo> getAllAgentInfo() {
        return BeanUtils.copyToList(baseMapper.selectList(), RemoteAgentVo.class);
    }
}
