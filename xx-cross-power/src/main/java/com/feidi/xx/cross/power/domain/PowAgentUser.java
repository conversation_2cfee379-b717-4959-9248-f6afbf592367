package com.feidi.xx.cross.power.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import java.io.Serial;

/**
 * 代理商用户对象 pow_agent_user
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pow_agent_user")
public class PowAgentUser extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 角色  AgentRoleType
     */
    private String role;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 密码
     */
    private String password;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 状态
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
