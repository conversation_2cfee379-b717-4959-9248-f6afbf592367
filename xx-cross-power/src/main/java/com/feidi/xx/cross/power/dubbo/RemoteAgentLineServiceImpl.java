package com.feidi.xx.cross.power.dubbo;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.operate.api.RemoteLineService;
import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;
import com.feidi.xx.cross.power.api.RemoteAgentLineService;
import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentLineVo;
import com.feidi.xx.cross.power.api.domain.line.bo.RemoteLineAgentBo;
import com.feidi.xx.cross.power.domain.PowAgent;
import com.feidi.xx.cross.power.domain.PowAgentLine;
import com.feidi.xx.cross.power.mapper.PowAgentLineMapper;
import com.feidi.xx.cross.power.mapper.PowAgentMapper;
import com.feidi.xx.cross.power.service.IPowAgentService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 线路服务
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteAgentLineServiceImpl implements RemoteAgentLineService {

    @DubboReference
    private final RemoteLineService remoteLineService;

    private final PowAgentMapper agentMapper;

    private final PowAgentLineMapper baseMapper;

    private final IPowAgentService agentService;

    /**
     * 根据代理商id获取代理商线路信息
     */


    /**
     * 匹配线路
     *
     * @param startAdCode 开始地区编码
     * @param endAdCode   结束地区编码
     * @return
     */
    @Override
    public RemoteAgentLineVo matchLine(String startAdCode, String endAdCode) {

        // 匹配线路
        Long matchLine = remoteLineService.matchLine(startAdCode, endAdCode);
        if (ObjectUtils.isNull(matchLine)) {
            return null;
        }
        RemoteLineVo lineVo = remoteLineService.queryByLineId(matchLine);
        if (ObjectUtils.isNull(lineVo)) {
            return null;
        }

        // 返回值
        RemoteAgentLineVo remoteAgentLineVo = new RemoteAgentLineVo();
        remoteAgentLineVo.setLineId(lineVo.getId());
        return remoteAgentLineVo;
    }

    /**
     * 根据代理商id获取代理商线路信息
     * @param agentId
     * @return
     */
    @Override
    public RemoteAgentLineVo getAgentLineByAgentIdAndLineId(Long agentId,Long lineId) {
        LambdaQueryWrapper<PowAgentLine> lqw = Wrappers.lambdaQuery();
        lqw.eq(PowAgentLine::getAgentId,agentId)
                .eq(PowAgentLine::getLineId,lineId);
        PowAgentLine powAgentLines = baseMapper.selectOne(lqw);
        return BeanUtils.copyProperties(powAgentLines, RemoteAgentLineVo.class);
    }


    /**
     * 获取代理商线路
     * @param agentIds
     * @return
     */
    @Override
    public List<RemoteAgentLineVo> getLineByAgentIds(List<Long> agentIds) {
        LambdaQueryWrapper<PowAgentLine> lqw = new LambdaQueryWrapper<PowAgentLine>()
                .in(CollUtil.isNotEmpty(agentIds), PowAgentLine::getAgentId, agentIds);
        List<PowAgentLine> pxAgentLines = baseMapper.selectList(lqw);
        List<RemoteAgentLineVo> agentLineVos = BeanUtils.copyToList(pxAgentLines, RemoteAgentLineVo.class);
        if (CollUtil.isNotEmpty(agentLineVos)) {
            List<PowAgent> agents = agentMapper.selectList();
            Map<Long, PowAgent> agentMap = StreamUtils.toMap(agents, PowAgent::getId, Function.identity());
            for (RemoteAgentLineVo agentLineVo : agentLineVos) {
                PowAgent agent = agentMap.get(agentLineVo.getAgentId());
                if (agent.getParentId() != null && agent.getParentId() > 0) {
                    PowAgent parent = agentMap.get(agent.getParentId());
                    agentLineVo.setParentId(parent.getId());
                }
            }
        }
        return agentLineVos;
    }

    /**
     * 获取代理商线路
     *
     * @param agentId 代理ID
     * @return
     */
    @Override
    public List<Long> getAgentLine(Long agentId) {
        return StreamUtils.toList(baseMapper.listByAgentId(agentId), PowAgentLine::getLineId);
    }

    @Override
    public List<Long> getLineAgent(Long lineId) {
        return StreamUtils.toList(baseMapper.listByLineId(lineId), PowAgentLine::getAgentId);
    }

    @Override
    public boolean assignAgent(RemoteLineAgentBo bo) {
        return agentService.assignAgent(bo);
    }


    @Override
    public List<RemoteAgentLineVo> listByParentId(Long parentId) {
        List<PowAgentLine> agentLines = baseMapper.listByParentId(parentId);
        return MapstructUtils.convert(agentLines, RemoteAgentLineVo.class);
    }


}
