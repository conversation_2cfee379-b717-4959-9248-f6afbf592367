package com.feidi.xx.cross.power.domain.vo;

import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.power.domain.PowAgentUser;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 代理商用户视图对象 pow_agent_user
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowAgentUser.class)
public class PowAgentUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    @ExcelProperty(value = "上级代理商id")
    private Long parentId;

    @ExcelProperty(value = "上级代理商企业主体名称")
    private String parentAgentName;

    /**
     * 代理商
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     *代理商名称
     */
    @ExcelProperty(value = "企业主体名称")
    private String companyName;

    /**
     * 税号
     */
    @ExcelProperty(value = "统一社会信用代码")
    private String taxNo;

    /**
     * 姓名
     */
    @ExcelProperty(value = "联系人姓名")
    private String name;

    /**
     * 角色[管理员|调度|财务]
     */
    private String role;

    /**
     * 手机号
     */
    @ExcelProperty(value = "联系人手机号")
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 所在城市
     */
    private String cityCode;

    /**
     * 密码
     */
    private String password;

    /**
     * 邀请码
     */
    @ExcelProperty(value = "邀请码")
    private String inviteCode;

    /**
     * 状态
     */

    @ExcelProperty(value = "账户状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = StatusEnum.class)
    private String status;
    private String statusText;

    /**
     * 招商地址
     */
    private String inviteUrl;

    /**
     * 招商广告
     */
    private String inviteImage;


}
