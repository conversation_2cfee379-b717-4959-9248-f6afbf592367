package com.feidi.xx.cross.power.dubbo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.domain.model.XcxLoginUser;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.power.DrvAuditStatusEnum;
import com.feidi.xx.cross.finance.api.RemoteDrvWalletService;
import com.feidi.xx.cross.finance.api.domain.vo.RemoteWalletVo;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.driver.bo.RemoteDriverQueryBo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteCarVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDrvLoginVo;
import com.feidi.xx.cross.power.domain.PowAgent;
import com.feidi.xx.cross.power.domain.PowCar;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.domain.vo.PowAgentVo;
import com.feidi.xx.cross.power.domain.vo.PowCarVo;
import com.feidi.xx.cross.power.domain.vo.PowDriverVo;
import com.feidi.xx.cross.power.mapper.PowAgentMapper;
import com.feidi.xx.cross.power.mapper.PowCarMapper;
import com.feidi.xx.cross.power.mapper.PowDriverMapper;
import com.feidi.xx.cross.power.service.IPowDriverService;
import com.feidi.xx.cross.power.service.IPowDriverVoucherService;
import com.feidi.xx.cross.power.service.impl.PowDriverServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 司机服务
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteDriverServiceImpl implements RemoteDriverService {

    private final IPowDriverService driverService;

    private final PowDriverMapper driverMapper;

    private final PowAgentMapper agentMapper;

    private final PowCarMapper carMapper;

    private final IPowDriverVoucherService voucherService;

    private final PowDriverServiceImpl driverServiceImpl;

    @DubboReference
    private final RemoteOrderService remoteOrderService;

    @DubboReference
    private final RemoteDrvWalletService remoteDrvWalletService;


    @Override
    public XcxLoginUser login(RemoteDrvLoginVo remoteDrvLoginVo) {
        PowDriverVo driverVo = driverService.login(remoteDrvLoginVo);
        // 整理返回值 这里的返回值关联到LoginHelper里的登录用户信息
        XcxLoginUser loginUser = new XcxLoginUser();
        loginUser.setOpenid(driverVo.getOpenId());
        loginUser.setNickname(driverVo.getName());
        loginUser.setStatus(driverVo.getStatus());
        loginUser.setTenantId(driverVo.getTenantId());
        loginUser.setAgentId(driverVo.getAgentId());
        loginUser.setParentId(driverVo.getParentId());
        loginUser.setUserId(driverVo.getId());
        loginUser.setUsername(driverVo.getName());
        loginUser.setUserPhone(driverVo.getPhone());
        loginUser.setUserType(UserTypeEnum.DRIVER_USER.getUserType());
        return loginUser;
    }

    @Override
    public RemoteDriverVo getDriverInfo(Long driverId) {
        PowDriver powDriver = driverMapper.selectById(driverId);
        Assert.notNull(powDriver, "司机信息不存在");
        String openId = voucherService.getOpenId(driverId);
        RemoteDriverVo remote = toRemote(powDriver);
        remote.setOpenId(openId);
        return remote;
    }

    @Override
    public RemoteDriverVo getDriverInfoByCode(String code) {
        PowDriver driver = driverMapper.getByCode(code);
        Assert.notNull(driver, "司机信息不存在");
        return toRemote(driver);
    }

    private RemoteDriverVo toRemote(PowDriver driver) {
        RemoteDriverVo remoteDriverVo = BeanUtils.copyProperties(driver, RemoteDriverVo.class);
        if (ArithUtils.isNotNull(driver.getAgentId())) {
            PowAgentVo pxAgentVo = agentMapper.selectVoById(driver.getAgentId());
            if (ObjectUtils.isNull(pxAgentVo)) {
                remoteDriverVo.setCompanyName(pxAgentVo.getCompanyName());
                remoteDriverVo.setAgentName(pxAgentVo.getName());
                remoteDriverVo.setAgentPhone(pxAgentVo.getPhone());
            }
        }
        return remoteDriverVo;
    }

    @Override
    public List<RemoteDriverVo> getDriverInfo(List<Long> driverIds) {
        // 司机信息
        List<RemoteDriverVo> ret = new ArrayList<>();
        List<PowDriverVo> pxDriverVos = driverMapper.selectVoBatchIds(driverIds);
        if (CollUtil.isNotEmpty(pxDriverVos)) {
            // 代理信息
            List<Long> agentIds = pxDriverVos.stream().map(PowDriverVo::getAgentId).toList();
            List<PowAgentVo> agentVos = agentMapper.selectVoBatchIds(agentIds);
            //代理商法人手机号
            Map<Long, PowAgentVo> agentVoMap = agentVos.stream().collect(Collectors.toMap(PowAgentVo::getId, Function.identity()));
            for (PowDriverVo vo : pxDriverVos) {
                RemoteDriverVo remoteDriverVo = BeanUtils.copyProperties(vo, RemoteDriverVo.class);
                // 代理
                if (ArithUtils.isNotNull(vo.getAgentId())) {
                    PowAgentVo powAgentVo = agentVoMap.get(vo.getAgentId());
                    if (ObjectUtil.isNotNull(powAgentVo)) {
                        remoteDriverVo.setCompanyName(powAgentVo.getCompanyName());
                        //TODO 代理商法人,手机号
//                        remoteDriverVo.setAgentName(powAgentVo.getName());
//                        remoteDriverVo.setAgentPhone(powAgentVo.getPhone());
                    }
                }
                ret.add(remoteDriverVo);
            }
        }
        return ret;
    }

    @Override
    public List<RemoteDriverVo> queryDriverInfo(RemoteDriverQueryBo bo) {
        List<PowDriver> drivers = driverMapper.selectList(Wrappers.<PowDriver>lambdaQuery()
                .eq(StrUtil.isNotBlank(bo.getAuditStatus()), PowDriver::getAuditStatus, bo.getAuditStatus())
                .like(StrUtil.isNotBlank(bo.getId()), PowDriver::getId, bo.getId())
                .like(StrUtil.isNotBlank(bo.getName()), PowDriver::getName, bo.getName())
                .like(StrUtil.isNotBlank(bo.getPhone()), PowDriver::getPhone, bo.getPhone())
                .in(CollUtil.isNotEmpty(bo.getAgentIds()), PowDriver::getAgentId, bo.getAgentIds())
                .nested(StrUtil.isNotBlank(bo.getUnionId()),
                        l -> l.like(StrUtil.isNotBlank(bo.getUnionId()), PowDriver::getId, bo.getUnionId()).or()
                                .like(StrUtil.isNotBlank(bo.getUnionId()), PowDriver::getName, bo.getUnionId()).or()
                                .like(StrUtil.isNotBlank(bo.getUnionId()), PowDriver::getPhone, bo.getUnionId()))
        );
        return BeanUtils.copyToList(drivers, RemoteDriverVo.class);    }

    @Override
    public RemoteDriverVo getDriverAndCar(Long driverId) {
        return TenantHelper.ignore(() -> {
            // 司机信息
            PowDriverVo powDriverVo = driverMapper.selectVoById(driverId);
            RemoteDriverVo remoteDriverVo = BeanUtils.copyProperties(powDriverVo, RemoteDriverVo.class);
            // 代理商信息
            if (ArithUtils.isNotNull(powDriverVo.getAgentId())) {
                PowAgent agent = agentMapper.selectById(powDriverVo.getAgentId());
                remoteDriverVo.setServicesPhone(agent.getServicesPhone());
                remoteDriverVo.setParentId(agent.getParentId());
            }
            // 车辆信息
            PowCarVo powCarVo = carMapper.selectVoOne(Wrappers.<PowCar>lambdaQuery().eq(PowCar::getDriverId, driverId).last("limit 1"));
            RemoteCarVo remoteCarVo = BeanUtils.copyProperties(powCarVo, RemoteCarVo.class);
            remoteDriverVo.setRemoteCarVo(remoteCarVo);
            return remoteDriverVo;
        });
    }

    @Override
    public RemoteDriverVo getDriverByDriverId(Long driverId) {
        // 司机信息
        PowDriverVo powDriverVo = driverMapper.selectVoById(driverId);
        if (ObjectUtils.isNull(powDriverVo)) {
            throw new ServiceException("司机信息不存在");
        }
        return BeanUtils.copyProperties(powDriverVo, RemoteDriverVo.class);    }

    @Override
    public boolean signOut(Long driverId) {
        // 校验司机是否有未完成的订单
        List<String> orderStatuses = Stream.of(OrderStatusEnum.RECEIVE, OrderStatusEnum.PICK, OrderStatusEnum.PICK_START, OrderStatusEnum.ING)
                .map(OrderStatusEnum::getCode).toList();
        List<RemoteOrderVo> remoteOrderVos = remoteOrderService.queryByDriverIdAndStatuses(LoginHelper.getUserId(), orderStatuses);
        if (CollUtil.isNotEmpty(remoteOrderVos)) {
            throw new ServiceException("您存在未结束的订单，请先结束订单");
        }

        // 校验钱包是否还有余额
        RemoteWalletVo wallet = remoteDrvWalletService.getWallet(driverId);
        if (wallet.getBalance() != 0 || wallet.getFreeze() != 0 || wallet.getCashing() != 0) {
            throw new ServiceException("您的账户还存在余额或存在未完成的提现，请提现完成后再注销");
        }

        LambdaUpdateWrapper<PowDriver> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(PowDriver::getStatus, UserStatusEnum.DELETED.getCode())
                .set(PowDriver::getUpdateTime, DateUtils.getNowDate())
                .eq(PowDriver::getId, driverId);
        return driverMapper.update(updateWrapper) > 0;
    }

    @Override
    public boolean updateStatus(Long driverId, String status) {
        return driverServiceImpl.updateStatus(driverId, status);
    }

    /**
     * 根据司机id查询司机信息
     *
     * @param driverIds 司机id集合
     * @return 司机信息
     */
    @Override
    public List<RemoteDriverVo> getDriverByIds(List<Long> driverIds) {
        if (CollUtil.isEmpty(driverIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PowDriver> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.in(PowDriver::getId, driverIds);
        return BeanUtils.copyToList(driverMapper.selectList(lambdaQuery), RemoteDriverVo.class);
    }

    @Override
    public List<RemoteDriverVo> listDriverByPhones(List<String> phones) {
        if (CollUtil.isEmpty(phones)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PowDriver> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.in(PowDriver::getPhone, phones);
        return BeanUtils.copyToList(driverMapper.selectList(lambdaQuery), RemoteDriverVo.class);
    }

    /**
     * 查询所有司机信息
     *
     * @return 司机信息
     */
    @Override
    public List<RemoteDriverVo> queryByDriverStatus(List<String> driverStatuses) {
        if (CollUtil.isEmpty(driverStatuses)) {
            return Collections.emptyList();
        }
        
        List<PowDriver> drivers = driverMapper.selectList(Wrappers.<PowDriver>lambdaQuery()
                        .eq(PowDriver::getAuditStatus, DrvAuditStatusEnum.SUCCESS.getCode())
                .in(PowDriver::getStatus, driverStatuses));

        return BeanUtils.copyToList(drivers, RemoteDriverVo.class);
    }
}
