<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.feidi.xx</groupId>
        <artifactId>xx-cross</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>xx-cross-power</artifactId>
    <description>
        xx-cross-power 运力模块
    </description>
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-common</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-common-oss</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-dfa</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-common-enum2text</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-common-id2name</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-api-order</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-api-operate</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-api-finance</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-api-power</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-api-market</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-common-push-provider</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-common-sms</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-api-auth</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>${zxing.version}</version>
        </dependency>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-api-message</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-common-pinyin-bom</artifactId>
            <version>${revision}</version>
            <type>pom</type>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.1.1</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>