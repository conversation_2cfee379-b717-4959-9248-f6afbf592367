package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单 - 排序
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CxSortEnum {
    // 订单排序
    CREATE_TIME_DESC("create_time_desc", "发单时间-降序"),
    GO_TIME_ASC("go_time_asc", "出发时间-升序"),
    PRICE_DESC("price_desc", "价格-降序");

    private final String code;
    private final String info;

}
