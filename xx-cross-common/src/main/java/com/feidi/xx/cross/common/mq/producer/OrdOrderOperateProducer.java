

package com.feidi.xx.cross.common.mq.producer;

import com.esotericsoftware.minlog.Log;
import com.feidi.xx.common.core.utils.TraceIdUtils;
import com.feidi.xx.common.rocketmq.base.BaseSendExtendDTO;
import com.feidi.xx.common.rocketmq.enums.MqMessageTypeEnum;
import com.feidi.xx.common.rocketmq.producer.AbstractProducer;
import com.feidi.xx.common.rocketmq.util.MQMessageUtil;
import com.feidi.xx.cross.common.constant.order.OrderRocketMQConstant;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;


/**
 * 订单操作记录消息生产者
 *
 * <AUTHOR>
 * @date 2025/3/20
 */
@Component
@RequiredArgsConstructor
public class OrdOrderOperateProducer extends AbstractProducer<OrdOrderOperateEvent> {
    public BaseSendExtendDTO buildBaseSendExtendParam(OrdOrderOperateEvent operateEvent) {
        Log.info("订单操作记录消息生产者-keys: {}", MDC.get(TraceIdUtils.TRACE_ID) + operateEvent.getOperateType());
        return BaseSendExtendDTO.builder()
                .eventName("订单操作记录")
                .keys(MDC.get(TraceIdUtils.TRACE_ID) + operateEvent.getOperateType() + operateEvent.getOrderId())
                .tag(operateEvent.getOrderId() + operateEvent.getOperateType())
                .messageType(MqMessageTypeEnum.ASYNC)
                .topic(OrderRocketMQConstant.XX_ORDER_OPERATE_TOPIC_KEY)
                .tag(operateEvent.getOperateType())
                .sentTimeout(2000L)
                .build();
    }
    @Override
    public SendResult sendMessage(OrdOrderOperateEvent operateEvent) {
        return MQMessageUtil.sendMessage(operateEvent, this::buildBaseSendExtendParam);
    }
}

