package com.feidi.xx.cross.common.cache.finance.enums;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.feidi.xx.cross.common.cache.finance.constants.FinanceCacheConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.Duration;
import java.util.Arrays;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 订单缓存key拼装枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FinCacheKeyEnum {
    /**
     * 订单询价缓存枚举 (缓存7天)
     */
    FIN_ORDER_BILL_KEY(FinanceCacheConstants.FIN_ORDER_BILL_KEY_PREFIX, 60 * 60 * 24 * 7,1,"见静态常量定义，参数1：订单对账时间"),

    ;
    /**
     * 前缀
     */
    private final String prefix;

    /**
     * 过期时间（单位：秒）
     */
    private final int duration;

    /**
     * 参数个数
     */
    private final int argsNum;

    /**
     * 描述 需要描述每一个参数都应该传什么
     */
    private final String desc;

    /**
     * 分隔符
     */
    private static final String SEPARATOR = ":";

    static {
        Set<String> set = Arrays.stream(values()).map(FinCacheKeyEnum::getPrefix).collect(Collectors.toSet());
        Assert.isTrue(values().length == set.size(), "存在部分枚举的prefix重复，无法初始化");
    }

    public String create(Object... args) {
        if (argsNum == 0) {
            return prefix;
        }
        //Assert.isTrue(args.length == argsNum, "参数个数不正确");
        return prefix + StrUtil.join(SEPARATOR, args);
    }

    /**
     * 获取过期时间
     * 在当前过期时间的基础上，浮动0%-3%，向下取整
     * 小于六十秒时，直接返回，不进行浮动
     *
     * @return 过期时间
     */
    public Duration getDuration() {
        if (duration < 60) {
            return Duration.ofSeconds(duration);
        }

        ThreadLocalRandom random = ThreadLocalRandom.current();
        // 生成随机浮动（1.00-1.03范围）
        double factor = 1 + random.nextDouble(0.03);
        int maxTime = (int) (duration * factor);

        // 确保结果不小于1（避免归零）
        maxTime = Math.max(maxTime, 1);

        return Duration.ofSeconds(maxTime);
    }
}
