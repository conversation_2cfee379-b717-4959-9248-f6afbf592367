package com.feidi.xx.cross.common.cache.order.vo;

import com.feidi.xx.cross.common.enums.operate.PricingStatusEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 订单轨迹位置信息
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@Data
public class OrdOrderTrackLocationCacheVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 定位时间 时间戳
     */
    private Long positionTime;

    /**
     *   方向角 0-359 顺时针方向
     */
    private Integer direction;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 状态
     */
    private String status;

}
