package com.feidi.xx.cross.common.cache.power.enums;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.feidi.xx.cross.common.cache.power.constants.PowerCacheConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.Duration;
import java.util.Arrays;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 *  运力缓存key拼装枚举
 * <AUTHOR>
@Getter
@AllArgsConstructor
public enum PowCacheKeyEnum {

    /**
     * 司机申请缓存枚举
     */
    POW_DRIVER_APPLY_KEY(PowerCacheConstants.DRIVER_APPLY_STORAGE, 60 * 24 * 7,1,"见静态常量定义，参数1：司机id "),

    /**
     * 司机申请 - 身份信息枚举
     */
    POW_DRIVER_APPLY_IDENTIFY_CACHE_KEY(PowerCacheConstants.DRIVER_APPLY_IDENTIFY_STORAGE,60 * 60 * 24 * 15, 1,"见静态常量定义，参数1：司机id "),

    /**
     * 司机申请 - 驾驶证枚举
     */
    POW_DRIVER_APPLY_DRIVING_CACHE_KEY(PowerCacheConstants.DRIVER_APPLY_DRIVING_STORAGE, 60 * 60 * 24 * 15, 1,"见静态常量定义，参数1：司机id "),

    /**
     * 司机申请 - 车辆信息枚举
     */
    POW_DRIVER_APPLY_CAR_CACHE_KEY(PowerCacheConstants.DRIVER_APPLY_CAR_STORAGE, 60 * 60 * 24 * 15, 1,"见静态常量定义，参数1：司机id "),

    /**
     * 司机申请 - 代扣协议信息枚举
     */
    POW_DRIVER_APPLY_AGREEMENT_CACHE_KEY(PowerCacheConstants.DRIVER_APPLY_AGREEMENT_STORAGE, 60 * 60 * 24 * 15, 1,"见静态常量定义，参数1：司机id "),

    /**
     * 司机组信息缓存枚举
     */
    POW_GROUP_INFO_CACHE_KEY(PowerCacheConstants.GROUP_INFO_CACHE_PREFIX, 60 * 60 * 24, 1,"见静态常量定义，参数1：司机组id"),

    /**
     * 代理商信息缓存枚举
     */
    POW_AGENT_INFO_CACHE_KEY(PowerCacheConstants.AGENT_INFO_CACHE_PREFIX, 60 * 60 * 24, 1,"见静态常量定义，参数1：代理商id"),

    /**
     * 代理商城市缓存枚举
     */
    POW_AGENT_CITY_CACHE_KEY(PowerCacheConstants.AGENT_CITY_CACHE_PREFIX, 60 * 60 * 24, 2,"见静态常量定义，参数1：代理商id,参数2: 城市id"),

    /**
     * 代理商线路缓存枚举
     */
    POW_AGENT_LINE_CACHE_KEY(PowerCacheConstants.AGENT_LINE_CACHE_PREFIX, 60 * 60 * 24, 2,"见静态常量定义，参数1：代理商id,参数2: 线路id"),

    /**
     * 代理商费率信息缓存枚举
     */
    POW_AGENT_RATE_CACHE_KEY(PowerCacheConstants.AGENT_RATE_CACHE_PREFIX, 60 * 60 * 24, 2,"见静态常量定义，参数1：代理商id,参数2：平台编码"),

    /**
     * 司机位置信息缓存枚举
     */
    POW_DRIVER_LOCATION_CACHE_KEY(PowerCacheConstants.DRIVER_LOCATION_KEY, 60 * 60 * 24, 1,"见静态常量定义，参数1：司机id"),

    /**
     * 司机信息缓存枚举
     */
    POW_DRIVER_INFO_CACHE_KEY(PowerCacheConstants.DRIVER_INFO_CACHE_PREFIX, 60 * 60 * 24, 1,"见静态常量定义，参数1：司机id"),

    /**
     * 司机线路信息缓存枚举
     */
    POW_DRIVER_LINE_CACHE_KEY(PowerCacheConstants.DRIVER_LINE_CACHE_PREFIX, 60 * 60 * 24, 2,"见静态常量定义，参数1：司机id,参数2:线路id"),

    /**
     * 司机佣金比例缓存枚举
     */
    POW_DRIVER_RATE_CACHE_KEY(PowerCacheConstants.DRIVER_RATE_CACHE_PREFIX, 60 * 60 * 24, 2,"见静态常量定义，参数1：司机id,参数2：平台编码"),

    /**
     * 车辆信息缓存枚举
     */
    POW_CAR_INFO_CACHE_KEY(PowerCacheConstants.CAR_INFO_CACHE_PREFIX, 60 * 60 * 24, 1,"见静态常量定义，参数1：车辆id"),

    /**
     * 车辆信息缓存枚举
     */
    POW_CAR_DRIVER_CACHE_KEY(PowerCacheConstants.CAR_DRIVER_CACHE_PREFIX, 60 * 60 * 24, 1,"见静态常量定义，参数1：司机id"),

    ;
    /**
     * 前缀
     */
    private final String prefix;

    /**
     * 过期时间（单位：秒）
     */
    private final int duration;

    /**
     * 参数个数
     */
    private final int argsNum;

    /**
     * 描述 需要描述每一个参数都应该传什么
     */
    private final String desc;

    /**
     * 分隔符
     */
    private static final String SEPARATOR = ":";

    static {
        Set<String> set = Arrays.stream(values()).map(PowCacheKeyEnum::getPrefix).collect(Collectors.toSet());
        Assert.isTrue(values().length == set.size(), "存在部分枚举的prefix重复，无法初始化");
    }

    public String create(Object... args) {
        if (argsNum == 0) {
            return prefix;
        }
        //Assert.isTrue(args.length == argsNum, "参数个数不正确");
        return prefix + StrUtil.join(SEPARATOR, args);
    }

    /**
     * 获取过期时间
     * 在当前过期时间的基础上，浮动0%-3%，向下取整
     * 小于六十秒时，直接返回，不进行浮动
     *
     * @return 过期时间
     */
    public Duration getDuration() {
        if (duration < 60) {
            return Duration.ofSeconds(duration);
        }

        ThreadLocalRandom random = ThreadLocalRandom.current();
        // 生成随机浮动（1.00-1.03范围）
        double factor = 1 + random.nextDouble(0.03);
        int maxTime = (int) (duration * factor);

        // 确保结果不小于1（避免归零）
        maxTime = Math.max(maxTime, 1);

        return Duration.ofSeconds(maxTime);
    }
}
