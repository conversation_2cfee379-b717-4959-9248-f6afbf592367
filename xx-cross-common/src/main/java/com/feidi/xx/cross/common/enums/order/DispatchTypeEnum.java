package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 城际 调度类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DispatchTypeEnum {

    // 0抢单 1自动抢单 2占单 3派单 4改派
    DRIVER_ROB("0", "抢单"),
    AGENT_ROB("1", "代理商自动抢单"),
    DRIVER_AUTO_ROB("2", "司机自动抢单"),
    ASSIGN("3", "派单"),
    CHANGE("4", "改派"),
    COMMON_AUTO_ROB("5", "常用路线自动抢单"),
    AGENT_PROXY_ROB("6", "运力代下单"),
    DRIVER_PROXY_ROB("7", "司机代下单"),
    RESELL_ROB("8", "订单转卖指派司机"),
    ;


    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static DispatchTypeEnum getByCode(String code) {
        for (DispatchTypeEnum itemEnum : DispatchTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DispatchTypeEnum itemEnum : DispatchTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

