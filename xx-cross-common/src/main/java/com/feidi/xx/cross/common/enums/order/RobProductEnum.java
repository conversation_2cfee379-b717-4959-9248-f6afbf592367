package com.feidi.xx.cross.common.enums.order;

import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RobProductEnum {
    // 自动抢单产品类型
    ALL("ALL", "全部"),
    RENT(ProductCodeEnum.RENT.getCode(), ProductCodeEnum.RENT.getInfo()),
    FIT(ProductCodeEnum.FIT.getCode(), ProductCodeEnum.FIT.getInfo());

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static RobProductEnum getByCode(String code) {
        for (RobProductEnum itemEnum : RobProductEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (RobProductEnum itemEnum : RobProductEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

