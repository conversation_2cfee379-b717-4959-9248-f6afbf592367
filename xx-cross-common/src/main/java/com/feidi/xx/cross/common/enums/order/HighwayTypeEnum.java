package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 城际 调度类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum HighwayTypeEnum {

    // 高速费类型 [1.乘客 2.司机 3.平摊]
    PASSENGER("1", "乘客", "愿意承担全部高速费"),
    DRIVER("2", "司机", "不承担高速费"),
    BISECT("3", "平摊", "愿意承担高速费");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 展示文本
     */
    private final String showText;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static HighwayTypeEnum getByCode(String code) {
        for (HighwayTypeEnum itemEnum : HighwayTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (HighwayTypeEnum itemEnum : HighwayTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    public static String getShowTextByCode(String code) {
        for (HighwayTypeEnum itemEnum : HighwayTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getShowText();
            }
        }
        return null;
    }
}

