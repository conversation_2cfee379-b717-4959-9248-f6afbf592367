package com.feidi.xx.cross.common.cache.finance.constants;

import com.feidi.xx.common.core.constant.GlobalConstants;

/**
 * 订单服务 缓存常量
 *
 * <AUTHOR>
 */
public interface FinanceCacheConstants {

    /**
     * 财务服务相关缓存前缀
     */
    String CACHE_PREFIX = "fin:";

    /**
     * 订单对账缓存前缀
     */
    String FIN_ORDER_BILL_KEY_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order:bill:";

    /**
     * 加盟司机组打款缓存前缀
     */
    String FIN_CASH_PAYOUTS_KEY_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "cash:payouts-alliance";
}
