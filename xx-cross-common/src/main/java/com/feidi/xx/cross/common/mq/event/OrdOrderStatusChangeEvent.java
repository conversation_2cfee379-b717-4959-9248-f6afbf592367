package com.feidi.xx.cross.common.mq.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class OrdOrderStatusChangeEvent {

    private Long orderId;
    private Long passengerId;
    private String orderNo;
    private String tenantId;
    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 司机ID
     */
    private Long driverId;
    /**
     * 产品类型[ProductCodeEnum]
     */
    private String productCode;

    /**
     * 订单金额[基础金额+附加费金额-客诉扣款金额]
     */
    private Long orderPrice;

    /**
     * 实际支付金额
     */
    private Long payPrice;
    /**
     * 乘客真实手机号
     */
    private String passengerPhone;

    /**
     * 订单状态[OrderStatusEnum]
     */
    private String status;

    /**
     * 第三方订单状态
     */
    private String thirdStatus;

    /**
     * 支付状态[PaymentStatusEnum]
     */
    private String payStatus;

    /**
     * 支付方式[PaymentTypeEnum]
     */
    private String payMode;
    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 支付单号
     */
    private String payNo;
    /**
     * 完成时间
     */
    private Date finishTime;
    /**
     * 是否客诉[IsYesEnum]
     */
    private String complain;
}
