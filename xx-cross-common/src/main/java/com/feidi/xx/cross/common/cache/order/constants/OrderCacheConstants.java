package com.feidi.xx.cross.common.cache.order.constants;

import com.feidi.xx.common.core.constant.GlobalConstants;

/**
 * 订单服务 缓存常量
 *
 * <AUTHOR>
 */
public interface OrderCacheConstants {

    /**
     * 订单服务相关缓存前缀
     */
    String CACHE_PREFIX = "ord:";

    /**
     * 订单估价缓存前缀
     */
    String ORD_ORDER_ESTIMATE_PRICE_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "estimate:price:";

    /**
     * 订单信息缓存前缀
     */
    String ORD_ORDER_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order:info:";

    /**
     * 订单关联信息缓存前缀
     */
    String ORD_ORDER_SUB_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order:sub:";

    /**
     * 订单司机信息缓存前缀
     */
    String ORD_ORDER_DRIVER_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order:driver:";

    /**
     * 订单调度司机信息缓存前缀
     */
    String ORD_ORDER_DISPATCH_DRIVER_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order:dispatch:driver:";

    /**
     * 订单分佣信息缓存前缀
     */
    String ORD_ORDER_RATE_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order:rate:";

    /**
     * 订单分佣信息缓存前缀
     */
    String ORD_ORDER_TYPE_RATE_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order:type:rate:";

    /**
     * 订单位置信息缓存前缀
     */
    String ORD_ORDER_POSITION_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order:position:rate:";

    /**
     * 订单位置信息缓存前缀
     */
    String ORD_ORDER_TYPE_POSITION_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order:type:position:";

    /**
     * 订单重复缓存前缀
     */
    String ORD_DUPLICATE_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order:duplicate:";

    /**
     * 投保状态前缀
     */
    String ORD_INSURANCE_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order:insurance:";

    /**
     * 乘客扫码下单前缀
     */
    String ORD_PRE_ORDER_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "pre:order:";
    /**
     * 订单位置前缀
     */
    String ORD_ORDER_LOCATION_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX +"order:location:";

    /**
     * 订单位置前缀
     */
    String ORD_ORDER_ERROR_EXCEL_PREFIX =  GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX +"order:error:excel";
}
