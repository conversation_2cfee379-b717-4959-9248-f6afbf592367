package com.feidi.xx.cross.common.mq.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrdOrderOperateEvent implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 路径追踪id
     */
    private String traceId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 时间戳
     */
    private Long timeStamp;

    /**
     * 请求参数
     */
    private String paramsJson;

    /**
     * 保险记录类型
     */
    private String recordType;

    /**
     * 响应参数
     */
    private String responseJson;

    /**
     * 状态
     */
    private String status;

    /**
     * 投保信息
     */
    private String insureContent;

    /**
     * 备注
     */
    private String remark;
}
