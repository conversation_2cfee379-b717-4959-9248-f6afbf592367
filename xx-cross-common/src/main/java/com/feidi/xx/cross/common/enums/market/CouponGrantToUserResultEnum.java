package com.feidi.xx.cross.common.enums.market;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CouponGrantToUserResultEnum {
    SUCCESS("0000", "发放成功"),
    FAIL("9999", "发放失败"),
    NOT_ENOUGH("1001", "库存不足"),
    ALREADY_GRANTED("1002", "已发放过该优惠券"),
    INVALID_COUPON("1003", "无效的优惠券"),
    //超过单人领取数量
    EXCEED_SINGLE_GRANT("1004", "超过单人领取数量"),
    ;

    private final String code;
    private final String message;

}
