package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账单类型枚举
 *
 * <AUTHOR>
 * @date 2025/3/17
 */
@Getter
@AllArgsConstructor
public enum RateTypeEnum {

    PLATFORM("0", "平台账单"),
    PARENT_AGENT("1", "父代理商账单"),
    AGENT("2", "代理商账单"),
    DRIVER("3", "司机账单"),
    INVITE_AGENT("4", "邀请有奖代理商账单"),
    INVITE_DRIVER("5", "邀请有奖司机账单"),
    RESELL_DRIVER("6", "订单转卖司机账单"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;
}
