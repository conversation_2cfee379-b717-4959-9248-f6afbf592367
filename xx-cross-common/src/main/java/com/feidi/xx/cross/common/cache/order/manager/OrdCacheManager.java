package com.feidi.xx.cross.common.cache.order.manager;

import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.order.enums.OrdCacheKeyEnum;
import com.feidi.xx.cross.common.cache.order.vo.OrdOrderTrackLocationCacheVo;
import com.feidi.xx.cross.order.api.RemoteOrderInfoService;
import com.feidi.xx.cross.order.api.RemoteOrderRateService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.RemotePositionService;
import com.feidi.xx.cross.order.api.domain.RemoteOrderDriverService;
import com.feidi.xx.cross.order.api.domain.vo.*;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 订单缓存管理器
 *
 * <AUTHOR>
 * @date 2025/3/17
 */
@Component
@RequiredArgsConstructor
public class OrdCacheManager {

    @DubboReference
    private final RemoteOrderService remoteOrderService;
    @DubboReference
    private final RemoteOrderInfoService remoteOrderInfoService;
    @DubboReference
    private final RemoteOrderDriverService remoteOrderDriverService;
    @DubboReference
    private final RemoteOrderRateService remoteOrderRateService;
    @DubboReference
    private final RemotePositionService remotePositionService;

    /**
     * 根据订单id获取订单关联信息（订单信息原则上不使用缓存，当前缓存信息仅供订单操作记录使用）
     *
     * @param orderNo 订单编号
     * @return 订单关联信息
     */
    public RemoteOrderVo getOrderInfoByOrderNo(String orderNo) {
        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_INFO_KEY.create(orderNo);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteOrderVo remoteOrderVo = remoteOrderService.queryByOrderNo(orderNo);

        RedisUtils.setCacheObject(cacheKey, remoteOrderVo, OrdCacheKeyEnum.ORD_ORDER_INFO_KEY.getDuration());

        return remoteOrderVo;
    }

    /**
     * 根据订单id获取订单关联信息
     *
     * @param orderId 订单id
     * @return 订单关联信息
     */
    public RemoteOrderInfoVo getOrderSubInfoByOrderId(Long orderId) {
//        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_SUB_INFO_KEY.create(orderId);
//
//        if (RedisUtils.hasKey(cacheKey)) {
//            return RedisUtils.getCacheObject(cacheKey);
//        }

        RemoteOrderInfoVo remoteOrderInfoVo = remoteOrderInfoService.queryByOrderId(orderId);

//        RedisUtils.setCacheObject(cacheKey, remoteOrderInfoVo, OrdCacheKeyEnum.ORD_ORDER_SUB_INFO_KEY.getDuration());

        return remoteOrderInfoVo;
    }

    /**
     * 根据订单id获取订单司机信息
     *
     * @param orderId 订单id
     * @return 订单司机信息
     */
    public RemoteOrderDriverVo getOrderDriverInfoByOrderId(Long orderId) {
        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_DRIVER_INFO_KEY.create(orderId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteOrderDriverVo remoteOrderInfoVo = remoteOrderDriverService.queryByOrderId(orderId);

        RedisUtils.setCacheObject(cacheKey, remoteOrderInfoVo, OrdCacheKeyEnum.ORD_ORDER_DRIVER_INFO_KEY.getDuration());

        return remoteOrderInfoVo;
    }

    /**
     * 根据订单id获取订单调度司机信息
     *
     * @param orderId 订单id
     * @return 订单调度司机信息
     */
    public RemoteOrderDriverVo getOrderDispatchDriverInfoByOrderId(Long orderId) {
        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_DISPATCH_DRIVER_INFO_KEY.create(orderId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteOrderDriverVo remoteOrderInfoVo = remoteOrderDriverService.queryDispatchByOrderId(orderId);

        RedisUtils.setCacheObject(cacheKey, remoteOrderInfoVo, OrdCacheKeyEnum.ORD_ORDER_DISPATCH_DRIVER_INFO_KEY.getDuration());

        return remoteOrderInfoVo;
    }


    /**
     * 根据订单id获取订单分佣信息
     *
     * @param orderId 订单id
     * @return 订单分佣信息
     */
    public List<RemoteOrderRateVo> getOrderRateInfoByOrderId(Long orderId) {
        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_RATE_INFO_KEY.create(orderId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheList(cacheKey);
        }

        List<RemoteOrderRateVo> remoteOrderRateVos = remoteOrderRateService.queryByOrderId(orderId);

        RedisUtils.setCacheList(cacheKey, remoteOrderRateVos, OrdCacheKeyEnum.ORD_ORDER_RATE_INFO_KEY.getDuration());

        return remoteOrderRateVos;
    }

    /**
     * 根据订单id和分佣类型获取订单分佣信息
     *
     * @param orderId 订单id
     * @return 订单分佣信息
     */
    public RemoteOrderRateVo getOrderRateInfoByOrderIdAndRateType(Long orderId, String rateType) {
        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_TYPE_RATE_INFO_KEY.create(orderId, rateType);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteOrderRateVo remoteOrderRateVo = remoteOrderRateService.queryByOrderIdAndRateType(orderId, rateType);

        RedisUtils.setCacheObject(cacheKey, remoteOrderRateVo, OrdCacheKeyEnum.ORD_ORDER_TYPE_RATE_INFO_KEY.getDuration());

        return remoteOrderRateVo;
    }

    /**
     * 根据订单id获取订单位置信息
     *
     * @param orderId 订单id
     * @return 订单位置信息集合
     */
    public List<RemotePositionVo> getOrderPositionByOrderId(Long orderId) {
        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_POSITION_INFO_KEY.create(orderId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheList(cacheKey);
        }

        List<RemotePositionVo> remotePositionVos = remotePositionService.queryByOrderId(orderId);

        RedisUtils.setCacheList(cacheKey, remotePositionVos, OrdCacheKeyEnum.ORD_ORDER_POSITION_INFO_KEY.getDuration());

        return remotePositionVos;
    }

    /**
     * 根据订单id和类型查询订单位置
     *
     * @param orderId 订单id
     * @param type    位置类型
     * @return 订单位置
     */
    public RemotePositionVo getOrderPositionByOrderIdAndType(Long orderId, String type) {
        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_TYPE_POSITION_INFO_KEY.create(orderId, type);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemotePositionVo remotePositionVo = remotePositionService.queryByOrderIdAndType(orderId, type);

        RedisUtils.setCacheObject(cacheKey, remotePositionVo, OrdCacheKeyEnum.ORD_ORDER_DRIVER_INFO_KEY.getDuration());

        return remotePositionVo;
    }

    /**
     * 根据乘客id添加邀请code
     */
    public void addInviteCode(Long passengerId, String inviteCode) {
        String cacheKey = OrdCacheKeyEnum.ORD_PRE_ORDER_INFO_KEY.create(passengerId);
        RedisUtils.setCacheObject(cacheKey, inviteCode, OrdCacheKeyEnum.ORD_PRE_ORDER_INFO_KEY.getDuration());
    }

    /**
     * 根据乘客id查询邀请code
     */
    public String getInviteCode(Long passengerId) {
        String cacheKey = OrdCacheKeyEnum.ORD_PRE_ORDER_INFO_KEY.create(passengerId);
        if (RedisUtils.hasKey(cacheKey)) {
            return  RedisUtils.getCacheObject(cacheKey);
        }
        return null;
    }
    /**
     * 根据乘客id删除邀请code
     */
    public void delInviteCode(Long passengerId) {
        String cacheKey = OrdCacheKeyEnum.ORD_PRE_ORDER_INFO_KEY.create(passengerId);
        if (RedisUtils.hasKey(cacheKey)) {
            RedisUtils.deleteObject(cacheKey);
        }
    }

    /**
     * 保存订单轨迹信息
     */
    public void saveOrderTrackLocation(OrdOrderTrackLocationCacheVo ordOrderTrackLocationCacheVo) {
        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_LOCATION_INFO_KEY.create(ordOrderTrackLocationCacheVo.getOrderId());
        RedisUtils.addCacheSortedSet(cacheKey, ordOrderTrackLocationCacheVo.getPositionTime(),ordOrderTrackLocationCacheVo, OrdCacheKeyEnum.ORD_ORDER_LOCATION_INFO_KEY.getDuration());
    }

    /**
     * 根据订单id获取订单轨迹信息最新一条数据
     */
    public OrdOrderTrackLocationCacheVo getOrderTrackLocationByOrderId(Long orderId) {
        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_LOCATION_INFO_KEY.create(orderId);
        if (RedisUtils.hasKey(cacheKey)) {
           return RedisUtils.getLastCacheSortedSet(cacheKey);
        }
        return null;
    }

    /**
     * 根据订单id获取所有订单轨迹信息
     */
    public List<OrdOrderTrackLocationCacheVo> getAllOrderTrackLocationByOrderId(Long orderId) {
        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_LOCATION_INFO_KEY.create(orderId);
        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheSortedSetAll(cacheKey);
        }
        return null;
    }
}
