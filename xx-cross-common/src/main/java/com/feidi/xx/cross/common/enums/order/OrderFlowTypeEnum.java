package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 城际 订单资金流向枚举
 */
@Getter
@AllArgsConstructor
public enum OrderFlowTypeEnum {

    NORMAL("0", "待提现"),
    CASHING("1", "提现中"),
    CASHED("2", "已提现"),
    TRANSFERRED("3", "已转出"),

    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static OrderFlowTypeEnum getByCode(String code) {
        for (OrderFlowTypeEnum itemEnum : OrderFlowTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (OrderFlowTypeEnum itemEnum : OrderFlowTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

