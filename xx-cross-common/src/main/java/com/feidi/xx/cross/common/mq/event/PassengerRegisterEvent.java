package com.feidi.xx.cross.common.mq.event;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class PassengerRegisterEvent implements Serializable {
    /**
     * 乘客id
     */
    @NotNull(message = "乘客id不能为空")
    private Long passengerId;
    /**
     * 小程序ID
     */
    private String appId;

    /**
     * openId
     */
    private String openId;

    /**
     * Uid
     */
    private String unionId;
    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 来源
     */
    private String source;
    /**
     * 注册时间
     */
    private Date registerTime;
    ;
    /**
     * 邀请类型
     */
    private String inviteType;

    /**
     * 登录类型
     */
    private String type;

    /**
     * 渠道
     */
    private String channel;
    /**
     * 邀请码
     */
    private String inviteCode;

}
