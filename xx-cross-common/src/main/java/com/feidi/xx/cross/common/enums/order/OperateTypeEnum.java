package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 城际 操作类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OperateTypeEnum {

    CANCEL("0", "取消", OrderStatusEnum.CANCEL.getCode()),
    INQUIRY("1", "询价", null),
    PLACE("2", "下单", OrderStatusEnum.CREATE.getCode()),
    RECEIVE("3", "接单", OrderStatusEnum.RECEIVE.getCode()),
    PAYMENT("4", "发起支付", null),
    VIRTUAL("5", "虚拟号", null),
    PICK("6", "出发去接乘客", OrderStatusEnum.PICK.getCode()),
    PICK_START("7", "到达起点", OrderStatusEnum.PICK_START.getCode()),
    INTO("8", "乘客上车-行程开始", OrderStatusEnum.ING.getCode()),
    INSURE("9", "投保", null),
    FINISH("10", "完成", OrderStatusEnum.FINISH.getCode()),
    REBATE("11", "结算", null),
    CANCEL_FEE("12", "取消费", null),
    INFO("13", "订单详情", null),
    LOCATION("14", "位置", null),
    CONFIRM_NOTIFY("15", "通知派单", null),
    CHANGE("16", "改派", null),
    REFUND("17", "退款", null),
    CONTACT_PASSENGER("18", "联系乘客", null),
    CANCEL_INSURE("19", "取消投保", null),
    PROFIT("20", "佣金计算", null),
    DRIVER_PHONE("21", "司机手机号", null),
    PAYMENT_SUCCESS("22", "支付成功", null),
    PAYMENT_FAIL("23", "支付失败", null),
    PAYMENT_QUERY("24", "刷新支付", null),
    INSURE_CALLBACK("25", "保险回调", null),
    COMPLAIN("26", "客诉", null),
    RESELL_REBATE("27", "订单转卖结算", null),
    UPDATE_EARLIEST_TIME("28", "修改乘客出发时间", null),
    ;


    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 订单状态
     */
    private final String orderStatus;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static OperateTypeEnum getByCode(String code) {
        for (OperateTypeEnum itemEnum : OperateTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (OperateTypeEnum itemEnum : OperateTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    /**
     * 根据CODE获取订单状态
     * @param code
     * @return
     */
    public static String getOrderStatusByCode(String code) {
        for (OperateTypeEnum itemEnum : OperateTypeEnum.values()) {
            if (Objects.equals(itemEnum.getCode(), code)) {
                return itemEnum.getOrderStatus();
            }
        }
        return null;
    }

    /**
     * 根据订单状态获取操作类型
     *
     * @param orderStatus 订单状态
     * @return
     */
    public static String getOperateTypeByOrderStatus(String orderStatus) {
        for (OperateTypeEnum itemEnum : OperateTypeEnum.values()) {
            if (Objects.equals(itemEnum.getOrderStatus(), orderStatus)) {
                return itemEnum.getCode();
            }
        }
        return null;
    }
}

