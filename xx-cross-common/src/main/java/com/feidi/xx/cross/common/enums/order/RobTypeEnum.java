package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 城际 操作类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RobTypeEnum {

    AUTO_GRAB_ORDER("0", "自动抢单"),
    FAVORITE_ROUTE("1", "常用路线");


    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static RobTypeEnum getByCode(String code) {
        for (RobTypeEnum itemEnum : RobTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (RobTypeEnum itemEnum : RobTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

