package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 城际 调度失败类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DispatchFailTypeEnum {

    // 调度失败原因【0取消 1已被抢 2PK失败 3系统原因 4第三方原因】
    CANCEL("0", "取消"),
    ROBBED("1", "已被抢"),
    PK("2", "PK失败"),
    SYSTEM("3", "系统原因"),
    THIRD("4", "第三方原因");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static DispatchFailTypeEnum getByCode(String code) {
        for (DispatchFailTypeEnum itemEnum : DispatchFailTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DispatchFailTypeEnum itemEnum : DispatchFailTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

