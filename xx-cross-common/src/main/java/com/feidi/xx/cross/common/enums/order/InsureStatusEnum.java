package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 投保状态
 *
 * <AUTHOR>
 * @date 2025/2/27
 */
@Getter
@AllArgsConstructor
public enum InsureStatusEnum {

    UNINSURED("0", "未投保"),
    INSURED("1", "已投保"),
    CANCEL_INSURE("2", "取消投保");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据code获取描述
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (InsureStatusEnum insureStatusEnum : values()) {
            if (Objects.equals(code, insureStatusEnum.getCode())) {
                return insureStatusEnum.getInfo();
            }
        }
        return null;
    }
}
