package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 城际 调度类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CancelTypeEnum {

    // 订单取消类型【0超时未接单 1超时未接单 2超时未支付 3客服取消 4乘客取消 5第三方接单取消 6客诉取消】
    TIME_OUT("0", "系统处理超时","订单超时取消"),
    UNPAID("1", "超时未支付",null),
    RECEIVE("2", "超时未接单",null),
    CUSTOMER_SERVICE("3", "客服取消",null),
    PASSENGER("4", "乘客取消","订单主动取消"),
    DRIVER("5", "司机取消","司机取消"),
    THIRD("6", "第三方接单取消",null),
    COMPLAIN("7", "客诉取消",null);

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 给乘客的展示文本
     */
    private final String showTextPsg;


    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static CancelTypeEnum getByCode(String code) {
        for (CancelTypeEnum itemEnum : CancelTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (CancelTypeEnum itemEnum : CancelTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    /**
     * 根据CODE获取给乘客的展示文本
     * @param code
     * @return
     */
    public static String getShowTextPsgByCode(String code) {
        for (CancelTypeEnum itemEnum : CancelTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getShowTextPsg();
            }
        }
        return null;
    }
}

