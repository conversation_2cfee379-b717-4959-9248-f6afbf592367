package com.feidi.xx.cross.common.enums.order;

import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 平台编码
 *
 * <AUTHOR>
 * @date 2024/8/29
 */
@Getter
@AllArgsConstructor
public enum PlatformCodeEnum {
    SELF("SELF", "自营", UserTypeEnum.SYS_USER, IsYesEnum.YES.getCode()),
    HBK("HBK", "哈啰", UserTypeEnum.HBK, IsYesEnum.NO.getCode()),
    MT("MT", "美团", UserTypeEnum.MT, IsYesEnum.YES.getCode()),
    TY("TY", "通用平台", null, IsYesEnum.NO.getCode()),
    ;

    private final String code;
    private final String info;
    private final UserTypeEnum userTypeEnum;
    private final String isShow;

    /**
     * 获取是用户信息
     *
     * @param code 编码
     * @return 用户类型枚举
     */
    public static UserTypeEnum getUserTypeEnumByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        for (PlatformCodeEnum value : PlatformCodeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getUserTypeEnum();
            }
        }
        return null;
    }

    /**
     * 获取信息
     *
     * @param code 编码
     * @return 平台名称
     */
    public static String getInfoByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        for (PlatformCodeEnum value : PlatformCodeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getInfo();
            }
        }
        return null;
    }

}
