package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 城际 操作类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RobPlanEnum {

    // 自动抢单类型
    ALL("ALL", "全部"),
    PART("PART", "区域");


    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static RobPlanEnum getByCode(String code) {
        for (RobPlanEnum itemEnum : RobPlanEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (RobPlanEnum itemEnum : RobPlanEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

