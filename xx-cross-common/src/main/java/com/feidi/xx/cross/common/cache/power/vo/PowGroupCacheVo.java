package com.feidi.xx.cross.common.cache.power.vo;

import com.feidi.xx.cross.common.enums.power.DriverGroupTypeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 司机组对象 px_group
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
public class PowGroupCacheVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商主体名称
     */
    private String mainBody;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 佣金比例
     */
    private BigDecimal rate;

    /**
     * 类型 {@link DriverGroupTypeEnum}
     */
    private String type;

    /**
     * 状态
     */
    private String status;

}
