package com.feidi.xx.cross.common.mq.producer;


import com.feidi.xx.common.core.utils.TraceIdUtils;
import com.feidi.xx.common.rocketmq.base.BaseSendExtendDTO;
import com.feidi.xx.common.rocketmq.enums.MqMessageTypeEnum;
import com.feidi.xx.common.rocketmq.producer.AbstractProducer;
import com.feidi.xx.common.rocketmq.util.MQMessageUtil;
import com.feidi.xx.cross.common.constant.order.OrderRocketMQConstant;
import com.feidi.xx.cross.common.mq.event.OrdOrderStatusChangeEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/**
 * 订单状态变更消息生产者
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrdOrderStatusChangeProducer extends AbstractProducer<OrdOrderStatusChangeEvent> {

    private BaseSendExtendDTO buildBaseSendExtendParam(OrdOrderStatusChangeEvent event) {
        log.info("订单状态变更消息生产者-keys: {}", MDC.get(TraceIdUtils.TRACE_ID) + event.getOrderId());
        return BaseSendExtendDTO.builder()
                .eventName("订单状态变更消息")
                .keys(MDC.get(TraceIdUtils.TRACE_ID) + event.getOrderId())
                .messageType(MqMessageTypeEnum.SYNC)
                .topic(OrderRocketMQConstant.XX_ORDER_STATUS_CHANGE_TOPIC_KEY)
                .sentTimeout(2000L)
                .build();
    }

    @Override
    public SendResult sendMessage(OrdOrderStatusChangeEvent event) {
        return MQMessageUtil.sendMessage(event, this::buildBaseSendExtendParam);
    }
}
