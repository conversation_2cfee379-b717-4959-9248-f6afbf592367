package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单轨迹类型枚举
 */
@Getter
@AllArgsConstructor
public enum OrderTrackTypeEnum {
    REAL_TRACK("0", "实时轨迹"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static OrderTrackTypeEnum getByCode(String code) {
        for (OrderTrackTypeEnum itemEnum : OrderTrackTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (OrderTrackTypeEnum itemEnum : OrderTrackTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

