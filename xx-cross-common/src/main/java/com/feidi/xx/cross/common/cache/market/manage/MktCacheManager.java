package com.feidi.xx.cross.common.cache.market.manage;

import cn.hutool.core.util.IdUtil;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.market.enums.MktCacheKeyEnum;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.common.constant.order.OrderLockKeyConstants;
import com.feidi.xx.cross.market.api.RemoteInviteConfigService;
import com.feidi.xx.cross.market.api.domain.RemoteMktInviteConfigVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * 营销缓存管理器
 *
 * <AUTHOR>
 * @date 2025/3/17
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MktCacheManager {

    @DubboReference
    private final RemoteInviteConfigService remoteInviteConfigService;


    /**
     * 判断用户是否可以下单
     *
     * @param userId
     * @return
     */
    public boolean canPlaceOrder(String userType, Long userId) {
        String key = MktCacheKeyEnum.MKT_AGENT_ORDER_LIMIT_KEY.create(userType, userId);
        Long currentCount = 0L;
        if (!RedisUtils.hasKey(key)) {
            RedisUtils.setAtomicValue(key, currentCount);
            recordOrder(key);
        }
        currentCount = RedisUtils.getAtomicValue(key);
        // 若当前计数已达上限（5次），则拒绝下单
        return currentCount < 5;
    }

    /**
     * 获取代客下单绑定乘客关系
     */
    public Boolean getPassengerId(String userType, Long userId, Long passengerId) {
        String key = MktCacheKeyEnum.MKT_AGENT_ORDER_BIND_PASSENGER_KEY.create(userType, userId, passengerId);
        return RedisUtils.hasKey(key);
    }

    /**
     * 代客下单绑定乘客关系
     */
    public void bindPassenger(String userType, Long userId, Long passengerId) {
        String key = MktCacheKeyEnum.MKT_AGENT_ORDER_BIND_PASSENGER_KEY.create(userType, userId, passengerId);
        if (!RedisUtils.hasKey(key)) {
            RedisUtils.setCacheObject(key, passengerId, MktCacheKeyEnum.MKT_AGENT_ORDER_BIND_PASSENGER_KEY.getDuration());
        }
    }


    /**
     * 设置下单次数过期时间
     *
     * @param key
     */
    private void recordOrder(String key) {
        // 第一次下单时，设置过期时间，保证每天 00:00 重置
        Long expireSeconds = getSecondsUntilMidnight();
        RedisUtils.expire(key, Duration.ofSeconds(expireSeconds));
    }

    /**
     * 获取距离明天 00:00 的秒数
     *
     * @return
     */
    private Long getSecondsUntilMidnight() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime midnight = now.toLocalDate().atStartOfDay().plusDays(1); // 次日00:00
        return Duration.between(now, midnight).getSeconds();
    }

    /**
     * 获取邀请有奖配置
     *
     * @param agentId
     */
    public RemoteMktInviteConfigVo getInviteConfigByAgentId(Long agentId) {
        String key = MktCacheKeyEnum.MKT_INVITE_CONFIG_KEY.create(agentId);
        RemoteMktInviteConfigVo mktInviteConfigVo;
        if (!RedisUtils.hasKey(key)) {
            mktInviteConfigVo = remoteInviteConfigService.getInviteConfigByAgentId(agentId);
            RedisUtils.setCacheObject(key, mktInviteConfigVo, MktCacheKeyEnum.MKT_INVITE_CONFIG_KEY.getDuration());
        } else {
            mktInviteConfigVo = RedisUtils.getCacheObject(key);
        }
        return mktInviteConfigVo;
    }

    /**
     * 优惠卷库存缓存
     *
     * @param couponId
     * @param stock
     * @param duration 秒
     */
    public void createCouponStock(Long couponId, Integer stock, Integer maxNum, long duration) {
        String stockKey = MktCacheKeyEnum.MKT_COUPON_STOCK_KEY.create(couponId);
        String userCountKey = MktCacheKeyEnum.MKT_COUPON_USER_COUNT_KEY.create(couponId);
        if (!RedisUtils.hasKey(stockKey)) {
            RedisUtils.setAtomicValue(stockKey, stock);
            RedisUtils.expire(stockKey, Duration.ofSeconds(duration));
        }
        if (!RedisUtils.hasKey(userCountKey)) {
            RedisUtils.setAtomicValue(userCountKey, maxNum);
            RedisUtils.expire(userCountKey, Duration.ofSeconds(duration));
        }
    }

    /**
     * 优惠卷库存缓存
     *
     * @param couponId
     * @param stock
     */
    public void createCouponStock(Long couponId, Integer stock, Integer maxNum) {
        String stockKey = MktCacheKeyEnum.MKT_COUPON_STOCK_KEY.create(couponId);
        String userCountKey = MktCacheKeyEnum.MKT_COUPON_USER_COUNT_KEY.create(couponId);
        Long duration = MktCacheKeyEnum.MKT_COUPON_STOCK_KEY.getDuration().getSeconds();
        if (!RedisUtils.hasKey(stockKey)) {
            RedisUtils.setAtomicValue(stockKey, stock);
            RedisUtils.expire(stockKey, Duration.ofSeconds(duration));
        }
        if (!RedisUtils.hasKey(userCountKey)) {
            RedisUtils.setAtomicValue(userCountKey, maxNum);
            RedisUtils.expire(userCountKey, Duration.ofSeconds(duration));
        }
    }

    /**
     * 删除优惠卷缓存
     *
     * @param couponId
     */
    public void deleteCouponStock(Long couponId) {
        String stockKey = MktCacheKeyEnum.MKT_COUPON_STOCK_KEY.create(couponId);
        RedisUtils.deleteObject(stockKey);
    }

    /**
     * 获取库存
     */
    public Long getCouponStock(Long couponId) {
        String stockKey = MktCacheKeyEnum.MKT_COUPON_STOCK_KEY.create(couponId);
        if (!RedisUtils.hasKey(stockKey)) {
            return null;
        }
        return RedisUtils.getAtomicValue(stockKey);
    }

    /**
     * 更新库存
     */
    public boolean updateCouponStock(Long couponId, Long passengerId) {
        String stockKey = MktCacheKeyEnum.MKT_COUPON_STOCK_KEY.create(couponId);
        if (!RedisUtils.hasKey(stockKey)) {
            return false;
        }
        long stockCount = RedisUtils.getAtomicValue(stockKey);
        if (stockCount <= 0) {
            log.info("优惠卷库存不足key:{}", stockKey);
            return false;
        }
        RedisUtils.decrAtomicValue(stockKey);
        return true;
    }

    /**
     * 库存回退
     */
    public void backCouponStock(Long couponId, Long passengerId) {
        String stockKey = MktCacheKeyEnum.MKT_COUPON_STOCK_KEY.create(couponId);
        String userKey = MktCacheKeyEnum.MKT_COUPON_USER_KEY.create(couponId, passengerId);
        if (!RedisUtils.hasKey(stockKey)) {
            return;
        }
        RedisUtils.incrAtomicValue(stockKey);
        if (RedisUtils.hasKey(userKey)) {
            RedisUtils.decrAtomicValue(userKey);
        }
    }


    /**
     * 加锁
     */
    public boolean lock(String couponId) {
        boolean result = false;
        RedissonClient client = RedisUtils.getClient();
        String lockKey = MktCacheKeyEnum.MKT_COUPON_STOCK_LOCK_KEY.create(couponId);
        RLock lock = client.getLock(lockKey);
        try {
            return result = lock.tryLock();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (lock.isLocked()) {
                lock.unlock();
            }
        }
        return result;
    }


    public String storeFileData(List<String> fileData) {
        String busKey = IdUtil.simpleUUID();
        String key = MktCacheKeyEnum.MKT_TARGETED_COUPONS_KEY.create(busKey);
        RedisUtils.setCacheObject(key, fileData, MktCacheKeyEnum.MKT_TARGETED_COUPONS_KEY.getDuration());
        return busKey;
    }

    public List<String> getFileData(String busKey) {
        String key = MktCacheKeyEnum.MKT_TARGETED_COUPONS_KEY.create(busKey);
        return RedisUtils.getCacheObject(key);
    }

}
