package com.feidi.xx.cross.common.cache.operate.enums;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.feidi.xx.cross.common.constant.operate.OperateCacheConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.Duration;
import java.util.Arrays;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 *  运营缓存key拼装枚举
 * <AUTHOR>
@Getter
@AllArgsConstructor
public enum OprCacheKeyEnum {

    /**
     * 平台信息缓存枚举
     */
    OPR_PLATFORM_INFO_KEY(OperateCacheConstants.PLATFORM_INFO_PREFIX, 60 * 30,1,"见静态常量定义，参数1：平台编码code "),

    /**
     * 平台信息缓存枚举
     */
    OPR_PLATFORM_KEY(OperateCacheConstants.PLATFORM_PREFIX, 60 * 30,2,"见静态常量定义，参数1：平台编码code, 参数2：appKey"),

    /**
     * 产品信息缓存枚举
     */
    OPR_PRODUCT_INFO_KEY(OperateCacheConstants.PRODUCT_INFO_PREFIX, 60 * 30, 1,"见静态常量定义，参数1：产品code"),

    /**
     * 路线信息缓存枚举
     */
    OPR_LINE_INFO_KEY(OperateCacheConstants.LINE_INFO_PREFIX, 60 * 30,1,"见静态常量定义，参数1：路线id "),

    /**
     * 节假日缓存枚举
     */
    OPR_HOLIDAY_INFO_KEY(OperateCacheConstants.HOLIDAY_KEY, 60 * 60 * 24 , 1,"见静态常量定义，参数1：日期"),

    /**
     * 定价缓存枚举
     */
    OPR_PRICING_INFO_KEY(OperateCacheConstants.PRICING_INFO_PREFIX, 60 * 60 * 24 * 30, 4,"见静态常量定义，参数1：平台code 参数2：价格id 参数3：产品code  参数4：日期类型"),

    /**
     * 城市缓存枚举
     */
    OPR_CITY_INFO_KEY(OperateCacheConstants.CITY_INFO_PREFIX, 60 * 30 * 24 *30, 1,"见静态常量定义，参数1：城市code"),

    /**
     * 城市价格缓存枚举
     */
    OPR_CITY_PRICE_INFO_KEY(OperateCacheConstants.CITY_PRICE_INFO_PREFIX, 60 * 30 * 24 *30, 2,"见静态常量定义，参数1：城市code, 参数2:渠道"),

    /**
     * 电子围栏缓存枚举
     */
    OPR_FENCE_INFO_KEY(OperateCacheConstants.FENCE_INFO_PREFIX, 60 * 30, 1,"见静态常量定义，参数1：城市code"),

    /**
     * 询价记录缓存枚举
     */
    OPR_ESTIMATE_RECORD_KEY(OperateCacheConstants.ESTIMATE_RECORD_PREFIX, 60 * 60 * 24, 1,"见静态常量定义，参数1：询价key"),


    /**
     * 价格信息缓存枚举
     */
    OPR_PRICE_INFO_KEY(OperateCacheConstants.PRICE_INFO_PREFIX ,60 * 60 * 24 * 30 ,1,"见静态常量定义，参数1：价格id" ),

    /**
     * 行政区划信息缓存枚举
     */
    OPR_DISTRICT_INFO_KEY(OperateCacheConstants.DISTRICT_INFO_PREFIX, 60 * 60 * 24 * 30, 1,"见静态常量定义，参数1：区adCode" )
    ;


    /**
     * 前缀
     */
    private final String prefix;

    /**
     * 过期时间（单位：秒）
     */
    private final int duration;

    /**
     * 参数个数
     */
    private final int argsNum;

    /**
     * 描述 需要描述每一个参数都应该传什么
     */
    private final String desc;

    /**
     * 分隔符
     */
    private static final String SEPARATOR = ":";

    static {
        Set<String> set = Arrays.stream(values()).map(OprCacheKeyEnum::getPrefix).collect(Collectors.toSet());
        Assert.isTrue(values().length == set.size(), "存在部分枚举的prefix重复，无法初始化");
    }

    public String create(Object... args) {
        if (argsNum == 0) {
            return prefix;
        }
        return prefix + StrUtil.join(SEPARATOR, args);
    }

    /**
     * 获取过期时间
     * 在当前过期时间的基础上，浮动0%-3%，向下取整
     * 小于六十秒时，直接返回，不进行浮动
     *
     * @return 过期时间
     */
    public Duration getDuration() {
        if (duration < 60) {
            return Duration.ofSeconds(duration);
        }

        ThreadLocalRandom random = ThreadLocalRandom.current();
        // 生成随机浮动（1.00-1.03范围）
        double factor = 1 + random.nextDouble(0.03);
        int floatedTime = (int) (duration * factor);
        // 确保结果不小于1（避免归零）
        int max = Math.max(floatedTime, 1);

        return  Duration.ofSeconds(max);
    }
}
