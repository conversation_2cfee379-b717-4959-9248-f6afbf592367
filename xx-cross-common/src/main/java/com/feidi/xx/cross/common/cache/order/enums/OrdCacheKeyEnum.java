package com.feidi.xx.cross.common.cache.order.enums;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.feidi.xx.cross.common.cache.order.constants.OrderCacheConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.Duration;
import java.util.Arrays;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 订单缓存key拼装枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrdCacheKeyEnum {
    /**
     * 订单询价缓存枚举
     */
    ORD_ORDER_ESTIMATE_PRICE_KEY(OrderCacheConstants.ORD_ORDER_ESTIMATE_PRICE_PREFIX, 60 * 60 * 2,2,"见静态常量定义，参数1：平台编号，参数2：估价key"),

    /**
     * 订单信息缓存枚举
     */
    ORD_ORDER_INFO_KEY(OrderCacheConstants.ORD_ORDER_INFO_PREFIX, 60 * 60 * 24,1,"见静态常量定义，参数1：订单编号 "),

    /**
     * 订单关联信息缓存枚举
     */
    ORD_ORDER_SUB_INFO_KEY(OrderCacheConstants.ORD_ORDER_SUB_INFO_PREFIX, 60 * 60 * 24,1,"见静态常量定义，参数1：订单id "),

    /**
     * 订单司机信息缓存枚举
     */
    ORD_ORDER_DRIVER_INFO_KEY(OrderCacheConstants.ORD_ORDER_DRIVER_INFO_PREFIX, 60 * 60 * 24,1,"见静态常量定义，参数1：订单id "),

    /**
     * 订单司机信息缓存枚举
     */
    ORD_ORDER_DISPATCH_DRIVER_INFO_KEY(OrderCacheConstants.ORD_ORDER_DISPATCH_DRIVER_INFO_PREFIX, 60 * 60 * 24,1,"见静态常量定义，参数1：订单id "),

    /**
     * 订单分佣信息缓存枚举
     */
    ORD_ORDER_RATE_INFO_KEY(OrderCacheConstants.ORD_ORDER_RATE_INFO_PREFIX, 60 * 60 * 24,1,"见静态常量定义，参数1：订单id"),

    /**
     * 订单分佣信息缓存枚举
     */
    ORD_ORDER_TYPE_RATE_INFO_KEY(OrderCacheConstants.ORD_ORDER_TYPE_RATE_INFO_PREFIX, 60 * 60 * 24,2,"见静态常量定义，参数1：订单id，参数2：分佣类型(rateType)"),

    /**
     * 订单位置信息缓存枚举
     */
    ORD_ORDER_POSITION_INFO_KEY(OrderCacheConstants.ORD_ORDER_POSITION_INFO_PREFIX, 60 * 60 * 24,1,"见静态常量定义，参数1：订单id"),

    /**
     * 订单位置信息缓存枚举
     */
    ORD_ORDER_TYPE_POSITION_INFO_KEY(OrderCacheConstants.ORD_ORDER_TYPE_POSITION_INFO_PREFIX, 60 * 60 * 24,2,"见静态常量定义，参数1：订单id, 参数2：位置类型(type)"),

    /**
     * 订单重复key
     */
    ORD_DUPLICATE_KEY(OrderCacheConstants.ORD_DUPLICATE_PREFIX, 60,1,"见静态常量定义，参数1：订单判重key或者id，返回值为订单判重key"),

    /**
     * 投保信息缓存枚举
     */
    ORD_ORDER_INSURANCE_INFO_KEY(OrderCacheConstants.ORD_INSURANCE_PREFIX, 60 * 60 * 24,1,"见静态常量定义，参数1：订单id"),

    /**
     * 乘客扫码下单缓存枚举
     */
    ORD_PRE_ORDER_INFO_KEY(OrderCacheConstants.ORD_PRE_ORDER_PREFIX, 60 * 20,1,"见静态常量定义，参数1：乘客id"),

    /**
     * 订单位置缓存枚举
     */
    ORD_ORDER_LOCATION_INFO_KEY(OrderCacheConstants.ORD_ORDER_LOCATION_PREFIX, 60 * 60 * 24 *3,1,"见静态常量定义，参数1：订单id"),

    /**
     * 代客下单异常EXCEL枚举
     */
    ORD_ORDER_ERROR_EXCEL_KEY(OrderCacheConstants.ORD_ORDER_ERROR_EXCEL_PREFIX, 60 * 60 * 6,1,"见静态常量定义，参数1：代理商登录人id"),

    ;
    /**
     * 前缀
     */
    private final String prefix;

    /**
     * 过期时间（单位：秒）
     */
    private final int duration;

    /**
     * 参数个数
     */
    private final int argsNum;

    /**
     * 描述 需要描述每一个参数都应该传什么
     */
    private final String desc;

    /**
     * 分隔符
     */
    private static final String SEPARATOR = ":";

    static {
        Set<String> set = Arrays.stream(values()).map(OrdCacheKeyEnum::getPrefix).collect(Collectors.toSet());
        Assert.isTrue(values().length == set.size(), "存在部分枚举的prefix重复，无法初始化");
    }

    public String create(Object... args) {
        if (argsNum == 0) {
            return prefix;
        }
        //Assert.isTrue(args.length == argsNum, "参数个数不正确");
        return prefix + StrUtil.join(SEPARATOR, args);
    }

    /**
     * 获取过期时间
     * 在当前过期时间的基础上，浮动0%-3%，向下取整
     * 小于六十秒时，直接返回，不进行浮动
     *
     * @return 过期时间
     */
    public Duration getDuration() {
        if (duration < 60) {
            return Duration.ofSeconds(duration);
        }

        ThreadLocalRandom random = ThreadLocalRandom.current();
        // 生成随机浮动（1.00-1.03范围）
        double factor = 1 + random.nextDouble(0.03);
        int maxTime = (int) (duration * factor);

        // 确保结果不小于1（避免归零）
        maxTime = Math.max(maxTime, 1);

        return Duration.ofSeconds(maxTime);
    }
}
