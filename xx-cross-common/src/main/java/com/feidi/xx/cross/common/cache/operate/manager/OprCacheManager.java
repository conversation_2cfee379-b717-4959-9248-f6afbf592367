package com.feidi.xx.cross.common.cache.operate.manager;

import com.feidi.xx.common.core.cache.system.DistrictCacheVo;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.operate.api.*;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityPriceVo;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityVo;
import com.feidi.xx.cross.operate.api.domain.fence.vo.RemoteFenceVo;
import com.feidi.xx.cross.operate.api.domain.holiday.vo.RemoteHolidayVo;
import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;
import com.feidi.xx.cross.operate.api.domain.platform.vo.RemotePlatformVo;
import com.feidi.xx.cross.operate.api.domain.price.vo.RemotePriceVo;
import com.feidi.xx.cross.operate.api.domain.product.vo.RemoteProductVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 运营缓存管理器
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
@Component
@RequiredArgsConstructor
public class OprCacheManager {

    @DubboReference
    private final RemoteCityService remoteCityService;
    @DubboReference
    private final RemoteFenceService remoteFenceService;
    @DubboReference
    private final RemoteHolidayService remoteHolidayService;
    @DubboReference
    private final RemotePlatformService remotePlatformService;
    @DubboReference
    private final RemoteProductService remoteProductService;
    @DubboReference
    private final RemoteLineService remoteLineService;
    @DubboReference
    private final RemotePriceService remotePriceService;

    @DubboReference
    private final RemoteCityPriceService remoteCityPriceService;




    /**
     * 根据城市编码获取城市信息
     *
     * @param code
     * @return
     */
    public RemoteCityVo getCityInfoByCode(String code) {
        String cacheKey = OprCacheKeyEnum.OPR_CITY_INFO_KEY.create(code);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteCityVo remoteCityVo = remoteCityService.queryByCityCode(code);

        RedisUtils.setCacheObject(cacheKey, remoteCityVo, OprCacheKeyEnum.OPR_CITY_INFO_KEY.getDuration());

        return remoteCityVo;
    }

    /**
     * 根据城市编码和平台code获取
     */
    public RemoteCityPriceVo getCityPriceInfoByCode(String code, String platformCode) {
        String cacheKey = OprCacheKeyEnum.OPR_CITY_PRICE_INFO_KEY.create(code, platformCode);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteCityPriceVo remoteCityPriceVo = remoteCityPriceService.queryByCityCode(code, platformCode);
        RedisUtils.setCacheObject(cacheKey, remoteCityPriceVo, OprCacheKeyEnum.OPR_CITY_PRICE_INFO_KEY.getDuration());

        return remoteCityPriceVo;
    }


    /**
     * 根据城市编码获取电子围栏信息
     *
     * @param cityCode
     * @return
     */
    public List<RemoteFenceVo> getFenceInfoByCityCode(String cityCode) {
        String cacheKey = OprCacheKeyEnum.OPR_FENCE_INFO_KEY.create(cityCode);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheList(cacheKey);
        }
        List<RemoteFenceVo> remoteFenceList = remoteFenceService.queryByCityCode(cityCode);
        RedisUtils.setCacheList(cacheKey, remoteFenceList, OprCacheKeyEnum.OPR_FENCE_INFO_KEY.getDuration());
        return remoteFenceList;
    }

    /**
     * 根据日期获取节假日信息
     *
     * @param date
     * @return
     */
    public RemoteHolidayVo getHolidayInfoByDate(String date) {
        String cacheKey = OprCacheKeyEnum.OPR_HOLIDAY_INFO_KEY.create(date);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteHolidayVo remoteHolidayVo = remoteHolidayService.queryByDate(date);
        RedisUtils.setCacheObject(cacheKey, remoteHolidayVo, OprCacheKeyEnum.OPR_HOLIDAY_INFO_KEY.getDuration());
        return remoteHolidayVo;
    }


    /**
     * 根据价格id获取价格信息
     */
    public RemotePriceVo getPriceInfoByPriceId(Long priceId) {
        String cacheKey = OprCacheKeyEnum.OPR_PRICE_INFO_KEY.create(priceId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }
        RemotePriceVo remotePriceVo = remotePriceService.getPriceInfoByPriceId(priceId);
        RedisUtils.setCacheObject(cacheKey, remotePriceVo, OprCacheKeyEnum.OPR_PRICE_INFO_KEY.getDuration());
        return remotePriceVo;
    }

    /**
     * 根据code获取平台信息
     *
     * @param code 平台code
     * @return 平台信息
     */
    public RemotePlatformVo getPlatformInfoByCode(String code) {
        String cacheKey = OprCacheKeyEnum.OPR_PLATFORM_INFO_KEY.create(code);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemotePlatformVo remotePlatformVo = remotePlatformService.queryByCode(code);

        RedisUtils.setCacheObject(cacheKey, remotePlatformVo, OprCacheKeyEnum.OPR_PLATFORM_INFO_KEY.getDuration());

        return remotePlatformVo;
    }

    /**
     * 根据code获取平台信息
     *
     * @param code   平台code
     * @param appKey appKey
     * @return 平台信息
     */
    public RemotePlatformVo getPlatformInfoByCodeAndAppKey(String code, String appKey) {
        String cacheKey = OprCacheKeyEnum.OPR_PLATFORM_KEY.create(code, appKey);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemotePlatformVo remotePlatformVo = remotePlatformService.queryByCodeAndAppKey(code, appKey);

        RedisUtils.setCacheObject(cacheKey, remotePlatformVo, OprCacheKeyEnum.OPR_PLATFORM_INFO_KEY.getDuration());

        return remotePlatformVo;
    }

    /**
     * 根据code获取产品信息
     *
     * @param code 产品code
     * @return 产品信息
     */
    public RemoteProductVo getProductInfoByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        String cacheKey = OprCacheKeyEnum.OPR_PRODUCT_INFO_KEY.create(code);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteProductVo remoteProductVo = remoteProductService.queryByCode(code);

        RedisUtils.setCacheObject(cacheKey, remoteProductVo, OprCacheKeyEnum.OPR_PRODUCT_INFO_KEY.getDuration());

        return remoteProductVo;
    }

    /**
     * 根据线路id获取线路信息
     *
     * @param lineId 线路id
     * @return 线路信息
     */
    public RemoteLineVo getLineInfoById(Long lineId) {
        if (ObjectUtils.isNull(lineId)) {
            return null;
        }

        String cacheKey = OprCacheKeyEnum.OPR_LINE_INFO_KEY.create(lineId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteLineVo remoteLineVo = remoteLineService.queryByLineId(lineId);

        RedisUtils.setCacheObject(cacheKey, remoteLineVo, OprCacheKeyEnum.OPR_LINE_INFO_KEY.getDuration());

        return remoteLineVo;
    }


    /**
     * 根据adCode 获取信息
     */
    public DistrictCacheVo getDistrictCacheVByAdCode(String adCode) {
        String cacheKey = SystemCacheKeyEnum.SYS_DISTRICT_INFO_KEY.create(adCode);
        DistrictCacheVo districtCacheVo = new DistrictCacheVo();
        if (!RedisUtils.hasKey(cacheKey)) {
            if (adCode == null || adCode.length() != 6 || adCode.endsWith("00")) {
                return districtCacheVo;
                // 非区/县级直接跳过
            }
            Map<String, String> stringStringMap = parseProvinceCityDistrict(adCode);
            //省市区查出来放进去
            String province = stringStringMap.get("province");
            SysDistrictCacheVo provinceValue = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_AD_CODE_KEY.create(), province);
                String city = stringStringMap.get("city");
                SysDistrictCacheVo cityValue = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_AD_CODE_KEY.create(), city);
            String district = stringStringMap.get("district");
            SysDistrictCacheVo districtValue = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_AD_CODE_KEY.create(), district);
            districtCacheVo.setProvinceId(provinceValue.getId());
            districtCacheVo.setProvince(provinceValue.getName());
            if (provinceValue.getIsDirect().equals("N")){
                if (cityValue != null) {
                    districtCacheVo.setCityId(cityValue.getId());
                    districtCacheVo.setCity(cityValue.getName());
                    districtCacheVo.setCityCode(cityValue.getCityCode());
                }
            }else {
                districtCacheVo.setCityId(provinceValue.getId());
                districtCacheVo.setCity(provinceValue.getName());
                districtCacheVo.setCityCode(provinceValue.getCityCode());
            }
            districtCacheVo.setDistrictId(districtValue.getId());
            districtCacheVo.setDistrict(districtValue.getName());
            RedisUtils.setCacheObject(cacheKey, districtCacheVo);
        } else {
            districtCacheVo = RedisUtils.getCacheObject(cacheKey);
        }
        return districtCacheVo;
    }

    // 解析省/市/区
    private Map<String, String> parseProvinceCityDistrict(String districtAdCode) {
        Map<String, String> result = new HashMap<>();
        String provinceAdcode = districtAdCode.substring(0, 2) + "0000";
        String cityAdcode = districtAdCode.substring(0, 4) + "00";
        result.put("province", provinceAdcode);
        result.put("city", cityAdcode);
        result.put("district", districtAdCode);
        return result;
    }

    /**
     *删除价格缓存
     */
    public void deletePriceCache(Long priceId) {
        String priceKey = OprCacheKeyEnum.OPR_PRICE_INFO_KEY.create(priceId);
        RedisUtils.deleteObject(priceKey);
        String priceDetailKey = OprCacheKeyEnum.OPR_PRICING_INFO_KEY.create(PlatformCodeEnum.TY.getCode(), priceId);
        priceDetailKey = priceDetailKey + ":*";
        Collection<String> keys = RedisUtils.keys(priceDetailKey);
        RedisUtils.deleteObject(keys);
    }



}
