package com.feidi.xx.cross.common.cache.operate.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.operate.api.domain.price.dto.PriceDto;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 询价记录视图对象 opr_estimate_record
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
@ExcelIgnoreUnannotated
public class OprEstimateRecordCacheVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 平台编码
     */
    @NotBlank(message = "平台编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformCode;

    /**
     * 线路id
     */
    @NotNull(message = "线路id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long lineId;

    /**
     * 计价模板id
     */
    @NotNull(message = "计价模板id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long priceId;

    /**
     * 起点省
     */
    @NotNull(message = "起点省不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startProvinceName;

    /**
     * 起点市
     */
    @NotNull(message = "起点市不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startCityName;

    /**
     * 起点市编码
     */
    @NotBlank(message = "起点市编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startCityCode;

    /**
     * 起点区
     */
    @NotNull(message = "起点区不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startDistrictName;

    /**
     * 起点区编码
     */
    @NotBlank(message = "起点区编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startAdCode;

    /**
     * 起点位置（长地址）
     */
    @NotBlank(message = "起点位置（长地址）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startAddress;

    /**
     * 起点位置（短地址）
     */
    @NotBlank(message = "起点位置（短地址）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startShortAddress;

    /**
     * 起点经度
     */
    @NotBlank(message = "起点经度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Double startLongitude;

    /**
     * 起点纬度
     */
    @NotBlank(message = "起点纬度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Double startLatitude;

    /**
     * 终点省
     */
    @NotNull(message = "终点省不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endProvinceName;

    /**
     * 终点市
     */
    @NotNull(message = "终点市不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endCityName;

    /**
     * 终点市编码
     */
    @NotBlank(message = "终点市编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endCityCode;

    /**
     * 终点区id
     */
    @NotNull(message = "终点区不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endDistrictName;

    /**
     * 终点区编码
     */
    @NotBlank(message = "终点区编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endAdCode;

    /**
     * 终点位置（长地址）
     */
    @NotBlank(message = "终点位置（长地址）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endAddress;

    /**
     * 终点位置（短地址）
     */
    @NotBlank(message = "终点位置（短地址）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endShortAddress;

    /**
     * 终点经度
     */
    @NotBlank(message = "终点经度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Double endLongitude;

    /**
     * 终点纬度
     */
    @NotBlank(message = "终点纬度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Double endLatitude;

    /**
     * 最早出发时间
     */
    @NotNull(message = "最早出发时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date earliestTime;

    /**
     * 最晚出发时间
     */
    @NotNull(message = "最晚出发时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date latestTime;

    /**
     * 里程
     */
    @NotNull(message = "里程不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer mileage;

    /**
     * 预计时长（单位：秒）
     */
    private Integer expectDuration;

    /**
     * 产品类型[productCodeEnum]
     */
    @NotBlank(message = "产品类型[productCodeEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productCode;

    /**
     * 乘客数量
     */
    @NotNull(message = "乘客数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer passengerNum;

    /**
     * 乘客详情
     */
    @NotBlank(message = "乘客详情不能为空", groups = { AddGroup.class, EditGroup.class })
    private String passengerDetail;

    /**
     * 返回的价格
     */
    @NotBlank(message = "返回的价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<PriceDto> price;

    /**
     * 计价KEY
     */
    @NotBlank(message = "计价KEY不能为空", groups = { AddGroup.class, EditGroup.class })
    private String estimateKey;
    /**
     * 路线类型 {@link com.feidi.xx.common.core.enums.StartEndEnum}
     */
    private String routeType;

}
