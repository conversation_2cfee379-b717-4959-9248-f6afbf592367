package com.feidi.xx.cross.common.cache.power.manager;

import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.power.enums.PowCacheKeyEnum;
import com.feidi.xx.cross.power.api.*;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentCityVo;
import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentLineVo;
import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentUserVo;
import com.feidi.xx.cross.power.api.domain.agentrate.vo.RemoteAgentRateVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteCarVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverLineVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverRateVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.cross.power.api.domain.group.vo.RemoteGroupVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 装饰增强
 */
@Component
@RequiredArgsConstructor
public class PowCacheManager {

    @DubboReference
    private final RemoteAgentService remoteAgentService;
    @DubboReference
    private final RemoteAgentUserService remoteAgentUserService;
    @DubboReference
    private final RemoteAgentCityService remoteAgentCityService;
    @DubboReference
    private final RemoteAgentRateService remoteAgentRateService;
    @DubboReference
    private final RemoteAgentLineService remoteAgentLineService;
    @DubboReference
    private final RemoteDriverService remoteDriverService;
    @DubboReference
    private final RemoteDriverLineService remoteDriverLineService;
    @DubboReference
    private final RemoteDriverRateService remoteDriverRateService;
    @DubboReference
    private final RemoteGroupService remoteGroupService;
    @DubboReference
    private final RemoteCarService remoteCarService;

    /**
     * 代理信息获取
     *
     * @param agentId 代理ID
     * @return 代理信息
     */

    public RemoteAgentVo getAgentInfoById(Long agentId) {
        return remoteAgentService.getAgentInfoById(agentId);
    }

    /**
     * 代理用户信息获取
     *
     * @param agentUserId 代理商用户信息id
     * @return
     */
    public RemoteAgentUserVo getAgentUserInfoById(Long agentUserId) {
        return remoteAgentUserService.getAgentUserInfoById(agentUserId);
    }

    /**
     * 根据代理商id获取代理商线路信息
     *
     * @param agentId
     * @param lineId
     * @return
     */
    public RemoteAgentLineVo getAgentLineInfoByAgentId(Long agentId, Long lineId) {
        if (ObjectUtils.isNull(agentId)) {
            return null;
        }

        String cacheKey = PowCacheKeyEnum.POW_AGENT_LINE_CACHE_KEY.create(agentId, lineId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteAgentLineVo remoteAgentLineVo = remoteAgentLineService.getAgentLineByAgentIdAndLineId(agentId, lineId);
        RedisUtils.setCacheObject(cacheKey, remoteAgentLineVo, PowCacheKeyEnum.POW_AGENT_LINE_CACHE_KEY.getDuration());
        return remoteAgentLineVo;
    }

    /**
     * 根据代理商id和城市id查询代理商城市信息
     *
     * @param agentId
     * @param cityId
     * @return
     */
    public RemoteAgentCityVo getAgentCityInfoByAgentIdAndCityId(Long agentId, Long cityId) {
        if (ObjectUtils.isNull(agentId) || ObjectUtils.isNull(cityId)) {
            return null;
        }

        String cacheKey = PowCacheKeyEnum.POW_AGENT_CITY_CACHE_KEY.create(agentId, cityId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteAgentCityVo remoteAgentCityVo = remoteAgentCityService.getAgentCityInfoByAgentIdAndCityId(agentId, cityId);

        RedisUtils.setCacheObject(cacheKey, remoteAgentCityVo, PowCacheKeyEnum.POW_AGENT_CITY_CACHE_KEY.getDuration());
        return remoteAgentCityVo;
    }

    /**
     * 根据代理商id和平台编码获取代理商利率信息
     *
     * @param agentId 代理商id
     * @param platformCode 平台编码
     * @return 代理商利率信息
     */
    public RemoteAgentRateVo getAgentRateByAgentIdAndPlatformCode(Long agentId, String platformCode) {
        if (ObjectUtils.isNull(agentId) || StringUtils.isBlank(platformCode)) {
            return null;
        }

        String cacheKey = PowCacheKeyEnum.POW_AGENT_RATE_CACHE_KEY.create(agentId, platformCode);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteAgentRateVo remoteAgentRateVo = remoteAgentRateService.getByAgentIdAndPlatformCode(agentId, platformCode);

        RedisUtils.setCacheObject(cacheKey, remoteAgentRateVo, PowCacheKeyEnum.POW_DRIVER_INFO_CACHE_KEY.getDuration());

        return remoteAgentRateVo;
    }

    /**
     * 根据司机id获取司机信息
     *
     * @param driverId 司机id
     * @return 司机信息
     */
    public RemoteDriverVo getDriverInfoById(Long driverId) {
        if (ObjectUtils.isNull(driverId)) {
            return null;
        }

        String cacheKey = PowCacheKeyEnum.POW_DRIVER_INFO_CACHE_KEY.create(driverId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteDriverVo remoteDriverVo = remoteDriverService.getDriverInfo(driverId);

        RedisUtils.setCacheObject(cacheKey, remoteDriverVo, PowCacheKeyEnum.POW_DRIVER_INFO_CACHE_KEY.getDuration());

        return remoteDriverVo;
    }

    /**
     * 根据司机id获取司机线路信息
     *
     * @param driverId 司机id
     * @return 司机线路信息
     */
    public List<RemoteDriverLineVo> getDriverLineByDriverId(Long driverId) {
        if (ObjectUtils.isNull(driverId)){
            return null;
        }
        String cacheKey = PowCacheKeyEnum.POW_DRIVER_LINE_CACHE_KEY.create(driverId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheList(cacheKey);
        }

        List<RemoteDriverLineVo> remoteDriverLineVos = remoteDriverLineService.getByDriverId(driverId);

        RedisUtils.setCacheList(cacheKey, remoteDriverLineVos, PowCacheKeyEnum.POW_DRIVER_LINE_CACHE_KEY.getDuration());

        return remoteDriverLineVos;
    }

    /**
     * 根据司机id和线路id获取司机线路信息
     *
     * @param driverId 司机id
     * @param lineId 线路id
     * @return 司机线路信息
     */
    public RemoteDriverLineVo getDriverLineByDriverIdAndLineId(Long driverId, Long lineId) {
        if (ObjectUtils.isNull(driverId) || ObjectUtils.isNull(lineId)){
            return null;
        }
        String cacheKey = PowCacheKeyEnum.POW_DRIVER_LINE_CACHE_KEY.create(driverId, lineId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteDriverLineVo remoteDriverLineVo = remoteDriverLineService.getByDriverIdAndLineId(driverId, lineId);

        RedisUtils.setCacheObject(cacheKey, remoteDriverLineVo, PowCacheKeyEnum.POW_DRIVER_LINE_CACHE_KEY.getDuration());
        return remoteDriverLineVo;
    }

    /**
     * 根据司机id和平台编码获取司机利率信息
     */
    public RemoteDriverRateVo getDriverRateByDriverIdAndPlatformCode(Long driverId, String platformCode) {
        if (ObjectUtils.isNull(driverId) || StringUtils.isBlank(platformCode)){
            return null;
        }
        String cacheKey = PowCacheKeyEnum.POW_DRIVER_RATE_CACHE_KEY.create(driverId, platformCode);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteDriverRateVo remoteDriverRateVo = remoteDriverRateService.getDriverRateByDriverIdAndPlatformCode(driverId, platformCode);

        RedisUtils.setCacheObject(cacheKey, remoteDriverRateVo, PowCacheKeyEnum.POW_DRIVER_RATE_CACHE_KEY.getDuration());
        return remoteDriverRateVo;
    }

    /**
     * 根据车辆id获取车辆信息
     *
     * @param carId 车辆id
     * @return 车辆信息
     */
    public RemoteCarVo getCarInfoById(Long carId) {
        if (ObjectUtils.isNull(carId)) {
            return null;
        }

        String cacheKey = PowCacheKeyEnum.POW_CAR_INFO_CACHE_KEY.create(carId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteCarVo remoteCarVo = remoteCarService.getCarInfoById(carId);

        RedisUtils.setCacheObject(cacheKey, remoteCarVo, PowCacheKeyEnum.POW_CAR_INFO_CACHE_KEY.getDuration());

        return remoteCarVo;
    }

    /**
     * 根据司机id获取车辆信息
     *
     * @param driverId 司机id
     * @return 车辆信息
     */
    public RemoteCarVo getCarInfoByDriverId(Long driverId) {
        if (ObjectUtils.isNull(driverId)) {
            return null;
        }

        String cacheKey = PowCacheKeyEnum.POW_CAR_DRIVER_CACHE_KEY.create(driverId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteCarVo remoteCarVo = remoteCarService.getCarInfoByDriverId(driverId);

        RedisUtils.setCacheObject(cacheKey, remoteCarVo, PowCacheKeyEnum.POW_CAR_DRIVER_CACHE_KEY.getDuration());

        return remoteCarVo;
    }

    /**
     * 根据司机组id获取司机组信息
     */
    public RemoteGroupVo getGroupInfoByGroupId(Long groupId) {
        if (ObjectUtils.isNull(groupId)){
            return null;
        }
        String cacheKey = PowCacheKeyEnum.POW_GROUP_INFO_CACHE_KEY.create(groupId);

        if (RedisUtils.hasKey(cacheKey)) {
            return RedisUtils.getCacheObject(cacheKey);
        }

        RemoteGroupVo remoteGroupVo = remoteGroupService.getGroupInfoByGroupId(groupId);
        RedisUtils.setCacheObject(cacheKey, remoteGroupVo, PowCacheKeyEnum.POW_CAR_DRIVER_CACHE_KEY.getDuration());
        return remoteGroupVo;
    }
}
