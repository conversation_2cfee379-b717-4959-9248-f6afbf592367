package com.feidi.xx.cross.common.cache.operate.vo;

import com.feidi.xx.cross.common.enums.operate.PricingStatusEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 价格缓存对象
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@Data
public class OprPriceCacheVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 平台code {@link PlatformCodeEnum}
     */
    private String platformCode;

    /**
     * 价格编码
     */
    private String priceCode;

    /**
     * 起步价格模板
     */
    private String initiateData;

    /**
     * 里程价格模板
     */
    private String mileageData;
    /**
     * 状态 {@link  PricingStatusEnum}
     */
    private String status;

}
