package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Stream;

/**
 * 订单 - 订单状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    CANCEL("0", "取消", "订单已取消", "已取消","已取消"),
    CREATE("1", "已下单", "立即抢单", null,"待司机接单"),
    RECEIVE("2", "司机接单", "出发去接乘客", "待服务","等待司机出发"),
    PICK("3", "司机出发接乘客", "到达上车点", "正在前往上车点","司机前往上车点"),
    PICK_START("4", "到达起点", "接到乘客", "等待乘客上车","司机到达上车点"),
    ING("5", "乘客上车（行程中）", "送达乘客", "正在前往终点","行程开始"),
    FINISH("6", "完成", "送达乘客", "已完成","已完成"),
    //SYSTEM_CLOSED("7", "系统关单", "系统关单", "系统关单","系统关单");
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 给司机的展示文本
     */
    private final String showTextDrv;

    /**
     * 给司机的IM会话的展示文本
     */
    private final String imTextDrv;

    /**
     * 给乘客的展示文本
     */
    private final String showTextPsg;
    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static OrderStatusEnum getByCode(String code) {
        for (OrderStatusEnum itemEnum : OrderStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (OrderStatusEnum itemEnum : OrderStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    public static String getShowTextDrvByCode(String code) {
        for (OrderStatusEnum itemEnum : OrderStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getShowTextDrv();
            }
        }
        return null;
    }

    public static String getShowTextPsgByCode(String code) {
        for (OrderStatusEnum itemEnum : OrderStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getShowTextPsg();
            }
        }
        return null;
    }

    public static String getImTextDrvByCode(String code) {
        for (OrderStatusEnum itemEnum : OrderStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getImTextDrv();
            }
        }
        return null;
    }

    /**
     * 服务中
     */
    public static List<String> getProcessingCodes() {
        return Stream.of(PICK, PICK_START, ING).map(OrderStatusEnum::getCode).toList();
    }

    /**
     * 待服务
     */
    public static List<String> getWaitingCodes() {
        return Stream.of(RECEIVE).map(OrderStatusEnum::getCode).toList();
    }

    /**
     * 取消
     */
    public static List<String> getCancelCodes() {
        return Stream.of(CANCEL).map(OrderStatusEnum::getCode).toList();
    }

    /**
     * 完成
     */
    public static List<String> getFinishCodes() {
        return Stream.of(FINISH).map(OrderStatusEnum::getCode).toList();
    }

    /**
     * 已下单
     *
     * @return
     */
    public static List<String> getCreateCodes() {
        return Stream.of(CREATE).map(OrderStatusEnum::getCode).toList();
    }

    /**
     * 结束状态
     */
    public static List<String> getOverCodes() {
        return Stream.of(CANCEL, FINISH).map(OrderStatusEnum::getCode).toList();
    }

    /**
     * 是否播报取消语音
     */
    public static List<String> getCancelPlayCodes() {
        return Stream.of(CREATE, RECEIVE, PICK, PICK_START, ING).map(OrderStatusEnum::getCode).toList();
    }

    /**
     * 获取乘客不可取消但是管理员可取消的状态
     * 仅自营可用！！！
     * 仅自营可用！！！
     * 仅自营可用！！！
     * @return
     */
    public static List<String> getCancelStatus() {
        return Stream.of(ING, FINISH).map(OrderStatusEnum::getCode).toList();
    }

    /**
     * 是否会话禁用的状态
     */
    public static List<String> getConversationOverCodes() {
        return Stream.of(CANCEL, CREATE, FINISH).map(OrderStatusEnum::getCode).toList();
    }

    /**
     * 获取可派单状态
     * @return
     */
    public static List<String> getDispatchStatus() {
        return Stream.of(CREATE, RECEIVE, PICK).map(OrderStatusEnum::getCode).toList();
    }

    /**
     * 获取完成状态
     * @return
     */
    public static List<String> getFinishedStatus() {
        return Stream.of(CANCEL, FINISH).map(OrderStatusEnum::getCode).toList();
    }

}
