package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 下单类型枚举
 *
 * <AUTHOR>
 * @date 2025/2/24
 */
@Getter
@AllArgsConstructor
public enum CreateModelEnum {

    THIRD_PLATFORM("0", "渠道订单","Y"),
    PASSENGER_QRCODE("1", "扫码下单","Y"),
    PASSENGER_ORDER("2", "乘客下单","Y"),
    DRIVER_ORDER("3", "司机代下单","Y"),
    AGENT_ORDER("4", "运力代下单","Y"),
//    ADMIN_ORDER("5", "总后台代下单"),
    RESELL_ORDER("6", "订单转卖", "Y"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    private final String isShow;

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return
     */
    public static String getInfoByCode(String code) {
        for (CreateModelEnum value : CreateModelEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getInfo();
            }
        }
        return null;
    }

}
