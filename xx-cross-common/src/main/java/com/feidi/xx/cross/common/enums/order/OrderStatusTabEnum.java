package com.feidi.xx.cross.common.enums.order;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 订单 - 订单状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderStatusTabEnum {
    ///  null.全部   2 未出行   5 行程中   0.取消   6完成   10.未出行&行程中
    ALL(null, "全部", null, "Y"),
    ORDERED(OrderStatusEnum.CREATE.getCode(), "未接单", Collections.singletonList(OrderStatusEnum.CREATE.getCode()), "Y"),
    NO_GO(OrderStatusEnum.RECEIVE.getCode(), "未出行", Arrays.asList(OrderStatusEnum.RECEIVE.getCode(), OrderStatusEnum.PICK.getCode(), OrderStatusEnum.PICK_START.getCode()), "Y"),
    NO_FINISH(OrderStatusEnum.ING.getCode(), "行程中", Collections.singletonList(OrderStatusEnum.ING.getCode()), "Y"),
    CANCEL(OrderStatusEnum.CANCEL.getCode(), "取消", Collections.singletonList(OrderStatusEnum.CANCEL.getCode()), "Y"),
    FINISH(OrderStatusEnum.FINISH.getCode(), "完成", Collections.singletonList(OrderStatusEnum.FINISH.getCode()), "Y"),
    NO_GO_AND_ING("10", "未出行&行程中", Arrays.asList(OrderStatusEnum.RECEIVE.getCode(), OrderStatusEnum.PICK.getCode(), OrderStatusEnum.PICK_START.getCode(), OrderStatusEnum.ING.getCode()), "N");

    private final String code;
    private final String info;
    private final List<String> statusList;
    private final String isShow;

    public static String getInfoByCode(String code){
        if(StrUtil.isEmpty(code)){
            return null;
        }
        for (OrderStatusTabEnum value : OrderStatusTabEnum.values()) {
            if(Objects.equals(value.getCode(), code)){
                return value.getInfo();
            }
        }
        return null;
    }

    public static List<String> getStatusListByCode(String code){
        if(StrUtil.isEmpty(code)){
            return null;
        }
        for (OrderStatusTabEnum value : OrderStatusTabEnum.values()) {
            if(Objects.equals(value.getCode(), code)){
                return value.getStatusList();
            }
        }
        return null;
    }

}
