package com.feidi.xx.cross.common.cache.power.constants;

import com.feidi.xx.common.core.constant.GlobalConstants;

/**
 * 运力服务 缓存常量
 *
 * <AUTHOR>
 */
public interface PowerCacheConstants {

    /**
     * 默认缓存时间长（5分钟）
     */
    Integer DEFAULT_TIME = 20;

    /**
     * 司机申请证件信息缓存时间（七天）
     */
    Integer DRIVER_APPLY_CACHE_TIME = 60 * 24 * 7;

    /**
     * 运力服务相关缓存前缀
     */
    String CACHE_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + "pow:";

    /**
     * 司机申请缓存key
     */
    String DRIVER_APPLY_STORAGE = CACHE_PREFIX +"driver_apply:storage:";

    /**
     * 代理商信息缓存Key前缀
     */
    String AGENT_INFO_CACHE_PREFIX = CACHE_PREFIX + "agent:info:";


    /**
     * 代理商城市缓存key前缀
     */
    String AGENT_CITY_CACHE_PREFIX = CACHE_PREFIX + "agent:city:";

    /**
     * 代理商线路缓存key前缀
     */
    String AGENT_LINE_CACHE_PREFIX = CACHE_PREFIX + "agent:line:";

    /**
     * 代理商利率信息缓存Key前缀
     */
    String AGENT_RATE_CACHE_PREFIX = CACHE_PREFIX + "agent:rate:";

    /**
     * 司机身份信息缓存
     */
    String DRIVER_APPLY_IDENTIFY_STORAGE= CACHE_PREFIX +"driver_apply_identify:storage:";

    /**
     * 司机加盟驾驶证信息缓存key
     */
    String DRIVER_APPLY_DRIVING_STORAGE  =CACHE_PREFIX+"driver_apply_driving:storage::";

    /**
     * 司机加盟车辆信息缓存key
     */
    String DRIVER_APPLY_CAR_STORAGE = CACHE_PREFIX+"driver_apply_car:storage::";
    /**
     * 司机加盟代扣协议信息缓存key
     */
    String DRIVER_APPLY_AGREEMENT_STORAGE = CACHE_PREFIX+"driver_apply_agreement:storage::";

    /**
     * 司机位置缓存key
     */
    String DRIVER_LOCATION_KEY = CACHE_PREFIX + "driver_location_info:storage::";

    /**
     * 司机线路信息缓存key前缀
     */
    String DRIVER_LINE_CACHE_PREFIX = CACHE_PREFIX + "driver:line:";

    /**
     * 司机佣金比例信息缓存Key前缀
     */
    String DRIVER_RATE_CACHE_PREFIX = CACHE_PREFIX + "driver:rate:";

    /**
     * 司机组信息缓存Key前缀
     */
    String GROUP_INFO_CACHE_PREFIX = CACHE_PREFIX + "group:";

    /**
     * 司机信息缓存Key前缀
     */
    String DRIVER_INFO_CACHE_PREFIX = CACHE_PREFIX + "driver:";

    /**
     * 车辆信息缓存Key前缀
     */
    String CAR_INFO_CACHE_PREFIX = CACHE_PREFIX + "car:";

    /**
     * 车辆信息缓存Key前缀
     */
    String CAR_DRIVER_CACHE_PREFIX = CACHE_PREFIX + "car::driver:";
}
