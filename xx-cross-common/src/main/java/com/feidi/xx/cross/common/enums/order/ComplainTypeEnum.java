package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 城际 客诉类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ComplainTypeEnum {

    // 【0人车不符】
    OTHER("0", "其他"),
    NOT_MATCH("1", "人车不符");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static ComplainTypeEnum getByCode(String code) {
        for (ComplainTypeEnum itemEnum : ComplainTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (ComplainTypeEnum itemEnum : ComplainTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

