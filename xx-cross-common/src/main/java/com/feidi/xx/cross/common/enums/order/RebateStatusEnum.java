package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 城际 返利状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RebateStatusEnum {

    // 订单返利状态【0未开始 1返利中 2已结算 3取消】
    NO("0", "未开始", "未开始"),
    ING("1", "返利中", "入账中"),
    FINISH("2", "已结算", "已入账"),
    CANCEL("3", "取消", "乘客主动取消"),
    COMPLAIN("4", "客诉", "客诉退款");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 给司机的展示文本
     */
    private final String showTextDrv;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static RebateStatusEnum getByCode(String code) {
        for (RebateStatusEnum itemEnum : RebateStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (RebateStatusEnum itemEnum : RebateStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    public static String getShowTextDrvByCode(String code) {
        for (RebateStatusEnum itemEnum : RebateStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getShowTextDrv();
            }
        }
        return null;
    }
}

