package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum YesOrNoEnum {
    YES(1, "是"),
    NO(0, "否");
    private final Integer code;
    private final String info;

    public static YesOrNoEnum getByCode(Integer code) {
        for (YesOrNoEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("code: " + code + " not exists");
    }
}
