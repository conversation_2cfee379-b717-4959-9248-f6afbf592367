<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.market.mapper.MktPassengerInviteRecordMapper">

    <select id="selectInviteStatisticsByCampaignIds" resultType="com.feidi.xx.cross.market.domain.vo.InviteStatisticsVo">
        SELECT
        campaign_id as campaignId,
        COUNT(*) AS invitedCount,
        COUNT(CASE WHEN status = '2' THEN 1 END) AS completedCount
        FROM
        mkt_passenger_invite_record
        WHERE
        campaign_id IN
        <foreach item="item" index="index" collection="campaignIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
        campaign_id
    </select>

    <resultMap id="inviteRecordDetailVo" type="com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordDetailVo">
        <id property="inviteRecordId" column="inviteRecordId" />
        <result property="campaignId" column="campaignId" />
        <result property="inviterId" column="inviterId" />
        <result property="inviterMobile" column="inviterMobile" />
        <result property="inviterCityCode" column="inviterCityCode" />
        <result property="inviteeId" column="inviteeId" />
        <result property="inviteeMobile" column="inviteeMobile" />
        <result property="inviteeCityCode" column="inviteeCityCode" />
        <result property="inviteTime" column="inviteTime" />
        <result property="orderCompleteTime" column="orderCompleteTime" />

        <result property="rewardId" column="rewardId" />
        <result property="roleType" column="roleType" />
        <result property="conditionType" column="conditionType" />
        <result property="rewardType" column="rewardType" />
        <result property="rewardValue" column="rewardValue" />
        <result property="rewardMeta" column="rewardMeta" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result property="rewardStatus" column="rewardStatus" />
        <result property="rewardTime" column="rewardTime" />
        <result property="refundRewardTime" column="refundRewardTime" />

        <result property="couponGrantId" column="couponGrantId" />
        <result property="couponStatus" column="couponStatus" />
        <result property="wipedTime" column="wipedTime" />
        <result property="orderNo" column="orderNo" />
    </resultMap>

    <select id="selectInviteRecordDetailPage" resultMap="inviteRecordDetailVo">
        SELECT
            mir.id AS inviteRecordId,
            mir.campaign_id AS campaignId,
            mir.inviter_id AS inviterId,
            mir.inviter_mobile AS inviterMobile,
            mir.inviter_city_code AS inviterCityCode,
            mir.invitee_id AS inviteeId,
            mir.invitee_mobile AS inviteeMobile,
            mir.invitee_city_code AS inviteeCityCode,
            mir.invite_time AS inviteTime,
            mir.order_complete_time AS orderCompleteTime,

            mpr.id AS rewardId,
            mpr.role_type AS roleType,
            mpr.condition_type AS conditionType,
            mpr.reward_type AS rewardType,
            mpr.reward_value AS rewardValue,
            mpr.reward_meta AS rewardMeta,
            mpr.status AS rewardStatus,
            mpr.create_time AS rewardTime,
            mpr.refund_reward_time AS refundRewardTime,

            mcg.id AS couponGrantId,
            mcg.using_status AS couponStatus,
            mcg.wiped_time AS wipedTime,
            mcg.order_no AS orderNo
        FROM
        mkt_passenger_invite_reward mpr
        LEFT JOIN
        mkt_passenger_invite_record mir ON mir.id = mpr.invite_record_id AND mpr.del_flag = '0'
        LEFT JOIN
            mkt_coupon_grant mcg ON mpr.id = mcg.source_id and mcg.source_type = '3' AND mcg.del_flag = '0'
        WHERE
            mir.del_flag = '0'
            <if test="bo.campaignId != null">
                AND mir.campaign_id = #{bo.campaignId}
            </if>
            <if test="bo.inviterId != null">
                AND mir.inviter_id = #{bo.inviterId}
            </if>
            <if test="bo.inviterMobile != null and bo.inviterMobile != ''">
                AND mir.inviter_mobile LIKE CONCAT('%', #{bo.inviterMobile}, '%')
            </if>
            <if test="bo.inviteeMobile != null and bo.inviteeMobile != ''">
                AND mir.invitee_mobile LIKE CONCAT('%', #{bo.inviteeMobile}, '%')
            </if>
            <if test="bo.inviteStatus != null and bo.inviteStatus != ''">
                AND mir.status = #{bo.inviteStatus}
            </if>
            <if test="bo.conditionType != null and bo.conditionType != ''">
                AND mpr.condition_type = #{bo.conditionType}
            </if>
            <if test="bo.rewardStatus != null and bo.rewardStatus != ''">
                AND mpr.status = #{bo.rewardStatus}
            </if>
            <if test="bo.couponStatus != null and bo.couponStatus != ''">
                AND mcg.using_status = #{bo.couponStatus}
            </if>
            <if test="bo.inviteTimeRange != null">
                AND mir.invite_time BETWEEN #{bo.inviteTimeRange.startTime} AND #{bo.inviteTimeRange.endTime}
            </if>
            <if test="bo.orderCompleteTimeRange != null">
                AND mir.order_complete_time BETWEEN #{bo.orderCompleteTimeRange.startTime} AND #{bo.orderCompleteTimeRange.endTime}
            </if>
    </select>
</mapper>
