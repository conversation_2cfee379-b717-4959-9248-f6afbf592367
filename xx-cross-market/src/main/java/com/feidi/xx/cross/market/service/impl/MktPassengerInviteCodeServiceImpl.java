package com.feidi.xx.cross.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.market.enums.MktCacheKeyEnum;
import com.feidi.xx.cross.market.domain.MktPassengerInviteCode;
import com.feidi.xx.cross.market.helper.CampaignInviteCodeHelper;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteCodeMapper;
import com.feidi.xx.cross.market.service.IMktPassengerInviteCodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import static com.feidi.xx.cross.market.helper.CampaignInviteCodeHelper.CODE_LENGTH;

/**
 * 乘客推乘客邀请码Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MktPassengerInviteCodeServiceImpl implements IMktPassengerInviteCodeService {

    private final MktPassengerInviteCodeMapper baseMapper;
    private final CampaignInviteCodeHelper campaignInviteCodeHelper;
    private final RedissonClient redissonClient;

    @Override
    public String createInviteCodeByUser(Long activityId, Long userId, String cityCode) {
        String key = MktCacheKeyEnum.INVITE_CODE_TEMP_KEY.create(activityId, userId, cityCode);
        Object o = RedisUtils.getCacheObject(key);
        if (o != null) {
            return o.toString();
        }
        String lockKey = "invite_code_lock:" + activityId + ":" + userId;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            lock.lock();
            String inviteCode = doGetInviteCode(activityId, userId, cityCode);
            RedisUtils.setCacheObject(key, inviteCode, MktCacheKeyEnum.INVITE_CODE_TEMP_KEY.getDuration());
            return inviteCode;
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private String doGetInviteCode(Long activityId, Long userId, String cityCode) {
        MktPassengerInviteCode mktPassengerInviteCode = baseMapper.selectOne(new LambdaQueryWrapper<MktPassengerInviteCode>()
                .eq(MktPassengerInviteCode::getActivityId, activityId)
                .eq(MktPassengerInviteCode::getInviterId, userId)
                .eq(MktPassengerInviteCode::getCityCode, cityCode)
        );
        if (mktPassengerInviteCode != null) {
            return mktPassengerInviteCode.getCode();
        }

        String inviteCode = generateUniqueInviteCode(activityId, userId);
        baseMapper.insert(MktPassengerInviteCode.builder()
                .activityId(activityId)
                .inviterId(userId)
                .cityCode(cityCode)
                .code(inviteCode)
                .build());
        return inviteCode;
    }

    /**
     * 生成唯一邀请码（避免重复）
     */
    private String generateUniqueInviteCode(Long activityId, Long userId) {
        int retryCount = 0;
        int maxRetry = 20;
        String inviteCode;

        do {
            inviteCode = campaignInviteCodeHelper.generateInviteCode(activityId, userId);
        } while (existsInviteCode(inviteCode) && retryCount++ < maxRetry);

        // 如果重试次数过多，调整长度
        if (retryCount >= maxRetry) {
            inviteCode = campaignInviteCodeHelper.generateInviteCodeWithLength(activityId, userId, CODE_LENGTH + (retryCount / 3));
        }

        return inviteCode;
    }


    private boolean existsInviteCode(String inviteCode) {
        Long count = baseMapper.selectCount(new LambdaQueryWrapper<MktPassengerInviteCode>()
                .eq(MktPassengerInviteCode::getCode, inviteCode));
        return count > 0;
    }

    @Override
    public Long getActivityIdByInviteCode(String inviteCode) {
        MktPassengerInviteCode mktPassengerInviteCode = baseMapper.selectOne(new LambdaQueryWrapper<MktPassengerInviteCode>()
                .eq(MktPassengerInviteCode::getCode, inviteCode));
        if (mktPassengerInviteCode == null) {
            return null;
        }
        return mktPassengerInviteCode.getActivityId();
    }
}
