package com.feidi.xx.cross.market.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.market.domain.MktPassengerInviteReward;
import com.feidi.xx.cross.market.domain.common.RewardMeta;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 乘客推乘客奖励发放记录视图对象 mkt_passenger_invite_reward
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktPassengerInviteReward.class)
public class MktPassengerInviteRewardVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 奖励记录ID
     */
    @ExcelProperty(value = "奖励记录ID")
    private Long id;

    /**
     * 活动ID
     */
    @ExcelProperty(value = "活动ID")
    private Long campaignId;

    /**
     * 对应邀请记录ID
     */
    @ExcelProperty(value = "对应邀请记录ID")
    private Long inviteRecordId;

    /**
     * 奖励配置ID
     */
    @ExcelProperty(value = "奖励配置ID")
    private Long rewardConfigId;

    /**
     * 奖励接收人乘客ID
     */
    @ExcelProperty(value = "奖励接收人乘客ID")
    private Long passengerId;

    /**
     * 角色类型(1邀请人,2被邀请人)
     */
    @ExcelProperty(value = "角色类型(1邀请人,2被邀请人)")
    private String roleType;

    /**
     * 发放条件(1注册成功,2完成首单)
     */
    @ExcelProperty(value = "发放条件(1注册成功,2完成首单)")
    private String conditionType;

    /**
     * 1=现金 2=优惠券 3=积分
     */
    @ExcelProperty(value = "1=现金 2=优惠券 3=积分")
    private String rewardType;

    /**
     * 奖励金额或积分
     */
    @ExcelProperty(value = "奖励金额或积分")
    private BigDecimal rewardValue;

    /**
     * 扩展字段(如发放的具体券ID数组)
     */
    @ExcelProperty(value = "扩展字段(如发放的具体券ID数组)")
    private RewardMeta rewardMeta;

    /**
     * 发放状态(1已发放,2发放失败,3客诉退券,4,客诉退券失败)
     */
    @ExcelProperty(value = "发放状态(1已发放,2发放失败,3客诉退券,4,客诉退券失败)")
    private String status;

    /**
     * 奖励退回时间
     */
    private Date refundRewardTime;
    /**
     * 备注
     */
    private String remark;
}
