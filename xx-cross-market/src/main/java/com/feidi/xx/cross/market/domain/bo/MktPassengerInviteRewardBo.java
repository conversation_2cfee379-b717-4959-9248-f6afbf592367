package com.feidi.xx.cross.market.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.market.domain.MktPassengerInviteReward;
import com.feidi.xx.cross.market.domain.common.RewardMeta;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 乘客推乘客奖励发放记录业务对象 mkt_passenger_invite_reward
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MktPassengerInviteReward.class, reverseConvertGenerate = false)
public class MktPassengerInviteRewardBo extends BaseEntity {

    /**
     * 奖励记录ID
     */
    @NotNull(message = "奖励记录ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long campaignId;

    /**
     * 对应邀请记录ID
     */
    @NotNull(message = "对应邀请记录ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long inviteRecordId;

    /**
     * 奖励配置ID
     */
    @NotNull(message = "奖励配置ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long rewardConfigId;

    /**
     * 奖励接收人乘客ID
     */
    @NotNull(message = "奖励接收人乘客ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long passengerId;

    /**
     * 角色类型(1邀请人,2被邀请人)
     */
    @NotNull(message = "角色类型(1邀请人,2被邀请人)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String roleType;

    /**
     * 发放条件(1注册成功,2完成首单)
     */
    @NotNull(message = "发放条件(1注册成功,2完成首单)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String conditionType;

    /**
     * 1=现金 2=优惠券 3=积分
     */
    @NotNull(message = "1=现金 2=优惠券 3=积分不能为空", groups = {AddGroup.class, EditGroup.class})
    private String rewardType;

    /**
     * 奖励金额或积分
     */
    @NotNull(message = "奖励金额或积分不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal rewardValue;

    /**
     * 扩展字段(如发放的具体券ID数组)
     */
    @NotBlank(message = "扩展字段(如发放的具体券ID数组)不能为空", groups = {AddGroup.class, EditGroup.class})
    private RewardMeta rewardMeta;

    /**
     * 发放状态(1已发放,2发放失败,3客诉退券,4,客诉退券失败)
     */
    @NotNull(message = "发放状态(1已发放,2发放失败,3客诉退券,4,客诉退券失败)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String status;

    /**
     * 奖励退回时间
     */
    private Date refundRewardTime;
    /**
     * 备注
     */
    private String remark;
}
