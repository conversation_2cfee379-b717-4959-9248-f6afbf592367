package com.feidi.xx.cross.market.helper;

import org.springframework.stereotype.Component;

import java.util.Random;

@Component
public class CampaignInviteCodeHelper {

    // 字符集，避免容易混淆的字符
    private static final String CHAR_SET = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789abcdefghjklmnpqrstuvwxyz";
    public static final int CODE_LENGTH = 14; // 邀请码长度

    /**
     * 生成随机邀请码
     */
    public String generateInviteCode(Long activityId, Long userId) {
        Random random = new Random(System.nanoTime() + activityId + userId);
        StringBuilder sb = new StringBuilder(CODE_LENGTH);

        for (int i = 0; i < CODE_LENGTH; i++) {
            int index = random.nextInt(CHAR_SET.length());
            sb.append(CHAR_SET.charAt(index));
        }

        return sb.toString();
    }


    /**
     * 生成指定长度的邀请码
     */
    public String generateInviteCodeWithLength(Long activityId, Long userId, int length) {
        Random random = new Random(System.nanoTime() + activityId + userId + length);
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            int index = random.nextInt(CHAR_SET.length());
            sb.append(CHAR_SET.charAt(index));
        }

        return sb.toString();
    }

}