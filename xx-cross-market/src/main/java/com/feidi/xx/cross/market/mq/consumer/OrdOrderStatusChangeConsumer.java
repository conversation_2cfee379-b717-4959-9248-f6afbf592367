package com.feidi.xx.cross.market.mq.consumer;

import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.rocketmq.base.MessageWrapper;
import com.feidi.xx.cross.common.constant.market.MarketRocketMQConstant;
import com.feidi.xx.cross.common.constant.order.OrderRocketMQConstant;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.mq.event.OrdOrderStatusChangeEvent;
import com.feidi.xx.cross.market.service.MktPassengerInviteCampaignJoinService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = OrderRocketMQConstant.XX_ORDER_STATUS_CHANGE_TOPIC_KEY,
        consumerGroup = MarketRocketMQConstant.XX_MARKET_ORDER_STATUS_CHANGE_CONSUMER_GROUP
)
@Slf4j
public class OrdOrderStatusChangeConsumer implements RocketMQListener<MessageWrapper<OrdOrderStatusChangeEvent>> {

    private final MktPassengerInviteCampaignJoinService mktPassengerInviteCampaignJoinService;

    @Override
    public void onMessage(MessageWrapper<OrdOrderStatusChangeEvent> message) {
        log.info("订单状态变更，消息内容：{}", message);
        OrdOrderStatusChangeEvent statusChangeEvent = message.getMessage();
        if (IsYesEnum.YES.getCode().equals(statusChangeEvent.getComplain())) {
            //客诉时，取消邀请活动的奖励
            mktPassengerInviteCampaignJoinService.revoke(statusChangeEvent);
        } else if (OrderStatusEnum.FINISH.getCode().equals(statusChangeEvent.getStatus())) {
            //完单时，处理邀请活动的奖励
            mktPassengerInviteCampaignJoinService.complete(statusChangeEvent);
        }
    }
}
