package com.feidi.xx.cross.market.service;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.cross.common.mq.event.OrdOrderStatusChangeEvent;
import com.feidi.xx.cross.common.mq.event.PassengerRegisterEvent;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 乘客邀请活动 参与活动
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MktPassengerInviteCampaignJoinService {

    private final MktPassengerInviteCampaignMbrService campaignMbrService;
    private final IMktPassengerInviteCodeService inviteCodeService;
    private final IMktPassengerInviteRewardConfigService inviteRewardConfigService;
    private final IMktPassengerInviteRecordService inviteRecordService;
    private final IMktPassengerInviteRewardService inviteRewardService;
    @DubboReference
    private final RemoteOrderService remoteOrderService;

    /**
     * 乘客注册成功，参与活动
     */
    @Transactional(rollbackFor = Exception.class)
    public void registrationJoin(PassengerRegisterEvent passengerRegisterEvent) {
        if (passengerRegisterEvent.getInviteCode() == null) {
            log.debug("乘客注册成功，参与活动，传递了空的邀请码 参数{}", passengerRegisterEvent);
            return;
        }
        //判断有没有参与过活动
        var isJoin = inviteRecordService.isJoin(passengerRegisterEvent.getPassengerId());
        if (isJoin) {
            log.warn("乘客注册成功，参与活动，但已参与过活动参数{}", passengerRegisterEvent);
            return;
        }
        var mktPassengerInviteCampaignVo = campaignMbrService.queryByInviteCode(passengerRegisterEvent.getInviteCode());
        log.debug("乘客注册成功，参与活动，活动信息：{}", mktPassengerInviteCampaignVo);
        if (mktPassengerInviteCampaignVo == null) {
            log.warn("乘客注册成功，参与活动，传递了邀请码 但活动不存在 参数{}", passengerRegisterEvent);
            return;
        }
        // 查询奖励配置
        List<MktPassengerInviteRewardConfigVo> rewardConfigVos = inviteRewardConfigService.queryListByCampaignId(mktPassengerInviteCampaignVo.getId());
        if (CollUtil.isEmpty(rewardConfigVos)) {
            log.warn("乘客注册成功，参与活动，活动不存在奖励配置 参数{}", passengerRegisterEvent);
            return;
        }

        //TODO
    }

    /**
     * 乘客客诉取消订单，取消邀请活动的奖励
     *
     * @param statusChangeEvent
     */
    @Transactional(rollbackFor = Exception.class)
    public void revoke(OrdOrderStatusChangeEvent statusChangeEvent) {
    }


    /**
     * 乘客完单，满足首单，发放邀请活动的奖励
     *
     * @param statusChangeEvent
     */
    @Transactional(rollbackFor = Exception.class)
    public void complete(OrdOrderStatusChangeEvent statusChangeEvent) {
        RemoteOrderVo remoteOrderVo = remoteOrderService.queryFirstOrderByPassengerId(statusChangeEvent.getPassengerId());
        if (remoteOrderVo == null) {
            log.error("查询乘客首单，订单不存在，event：{}", statusChangeEvent);
            return;
        }
        if (!remoteOrderVo.getOrderNo().equals(statusChangeEvent.getOrderNo())) {
            log.debug("不是首单 不用处理邀请活动相关的，event：{}，remoteOrderVo：{}", statusChangeEvent, remoteOrderVo);
            return;
        }

        //TODO
    }
}
