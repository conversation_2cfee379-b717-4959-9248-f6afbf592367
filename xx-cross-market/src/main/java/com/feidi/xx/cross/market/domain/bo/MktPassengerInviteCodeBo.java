package com.feidi.xx.cross.market.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.market.domain.MktPassengerInviteCode;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 乘客推乘客邀请码业务对象 mkt_passenger_invite_code
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MktPassengerInviteCode.class, reverseConvertGenerate = false)
public class MktPassengerInviteCodeBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 邀请码
     */
    @NotBlank(message = "邀请码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String code;

    /**
     * 邀请人ID
     */
    @NotNull(message = "邀请人ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long inviterId;

    /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long activityId;

    /**
     * 城市编码
     */
    private String cityCode;
}
