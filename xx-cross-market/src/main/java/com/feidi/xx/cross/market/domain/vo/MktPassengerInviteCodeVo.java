package com.feidi.xx.cross.market.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.market.domain.MktPassengerInviteCode;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 乘客推乘客邀请码视图对象 mkt_passenger_invite_code
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktPassengerInviteCode.class)
public class MktPassengerInviteCodeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 邀请码
     */
    @ExcelProperty(value = "邀请码")
    private String code;

    /**
     * 邀请人ID
     */
    @ExcelProperty(value = "邀请人ID")
    private Long inviterId;

    /**
     * 活动ID
     */
    @ExcelProperty(value = "活动ID")
    private Long activityId;

    /**
     * 城市编码
     */
    private String cityCode;
}
