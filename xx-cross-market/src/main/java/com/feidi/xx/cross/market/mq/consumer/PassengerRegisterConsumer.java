package com.feidi.xx.cross.market.mq.consumer;


import com.feidi.xx.common.rocketmq.base.MessageWrapper;
import com.feidi.xx.cross.common.constant.market.MarketRocketMQConstant;
import com.feidi.xx.cross.common.constant.member.MemberRocketMQConstant;
import com.feidi.xx.cross.common.mq.event.PassengerRegisterEvent;
import com.feidi.xx.cross.market.service.MktPassengerInviteCampaignJoinService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = MemberRocketMQConstant.XX_PASSENGER_REGISTER_TOPIC_KEY,
        consumerGroup = MarketRocketMQConstant.XX_MARKET_PASSENGER_REGISTER_CONSUMER_GROUP
)
@Slf4j
public class PassengerRegisterConsumer implements RocketMQListener<MessageWrapper<PassengerRegisterEvent>> {

    @Autowired
    private MktPassengerInviteCampaignJoinService mktPassengerInviteCampaignJoinService;

    @Override
    public void onMessage(MessageWrapper<PassengerRegisterEvent> message) {
        log.info("乘客注册成功，消息内容：{}", message);
        PassengerRegisterEvent registerEvent = message.getMessage();
        mktPassengerInviteCampaignJoinService.registrationJoin(registerEvent);
    }
}
