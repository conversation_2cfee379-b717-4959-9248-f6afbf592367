package com.feidi.xx.cross.market.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.*;

import java.io.Serial;

/**
 * 乘客推乘客邀请码对象 mkt_passenger_invite_code
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mkt_passenger_invite_code")
public class MktPassengerInviteCode extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 邀请码
     */
    private String code;

    /**
     * 邀请人ID
     */
    private Long inviterId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
