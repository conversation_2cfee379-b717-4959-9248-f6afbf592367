package com.feidi.xx.cross.market.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.enums.market.*;
import com.feidi.xx.cross.market.domain.MktCoupon;
import com.feidi.xx.cross.market.domain.MktPassengerInviteReward;
import com.feidi.xx.cross.market.domain.bo.CouponGrantToUserBo;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRewardBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardVo;
import com.feidi.xx.cross.market.mapper.MktCouponMapper;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteRewardMapper;
import com.feidi.xx.cross.market.service.IMktCouponGrantService;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRewardService;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import com.feidi.xx.resource.api.RemoteSmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 乘客推乘客奖励发放记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MktPassengerInviteRewardServiceImpl implements IMktPassengerInviteRewardService {

    private final MktPassengerInviteRewardMapper baseMapper;
    private final IMktCouponGrantService mktCouponGrantService;
    private final MktCouponMapper mktCouponMapper;
    @DubboReference
    private final RemoteSmsService remoteSmsService;
    @DubboReference
    private final RemotePassengerService remotePassengerService;

    /**
     * 查询乘客推乘客奖励发放记录
     *
     * @param id 主键
     * @return 乘客推乘客奖励发放记录
     */
    @Override
    public MktPassengerInviteRewardVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客推乘客奖励发放记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客奖励发放记录分页列表
     */
    @Override
    public TableDataInfo<MktPassengerInviteRewardVo> queryPageList(MktPassengerInviteRewardBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktPassengerInviteReward> lqw = buildQueryWrapper(bo);
        Page<MktPassengerInviteRewardVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客推乘客奖励发放记录列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客奖励发放记录列表
     */
    @Override
    public List<MktPassengerInviteRewardVo> queryList(MktPassengerInviteRewardBo bo) {
        LambdaQueryWrapper<MktPassengerInviteReward> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktPassengerInviteReward> buildQueryWrapper(MktPassengerInviteRewardBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktPassengerInviteReward> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCampaignId() != null, MktPassengerInviteReward::getCampaignId, bo.getCampaignId());
        lqw.eq(bo.getInviteRecordId() != null, MktPassengerInviteReward::getInviteRecordId, bo.getInviteRecordId());
        lqw.eq(bo.getRewardConfigId() != null, MktPassengerInviteReward::getRewardConfigId, bo.getRewardConfigId());
        lqw.eq(bo.getPassengerId() != null, MktPassengerInviteReward::getPassengerId, bo.getPassengerId());
        lqw.eq(bo.getRoleType() != null, MktPassengerInviteReward::getRoleType, bo.getRoleType());
        lqw.eq(bo.getConditionType() != null, MktPassengerInviteReward::getConditionType, bo.getConditionType());
        lqw.eq(bo.getRewardType() != null, MktPassengerInviteReward::getRewardType, bo.getRewardType());
        lqw.eq(bo.getRewardValue() != null, MktPassengerInviteReward::getRewardValue, bo.getRewardValue());
        lqw.eq(bo.getStatus() != null, MktPassengerInviteReward::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增乘客推乘客奖励发放记录
     *
     * @param bo 乘客推乘客奖励发放记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktPassengerInviteRewardBo bo) {
        MktPassengerInviteReward add = MapstructUtils.convert(bo, MktPassengerInviteReward.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改乘客推乘客奖励发放记录
     *
     * @param bo 乘客推乘客奖励发放记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktPassengerInviteRewardBo bo) {
        MktPassengerInviteReward update = MapstructUtils.convert(bo, MktPassengerInviteReward.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktPassengerInviteReward entity) {
    }

    /**
     * 校验并批量删除乘客推乘客奖励发放记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 创建并发放奖励
     *
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfigVo 奖励配置
     * @param passengerId    奖励接收人ID
     * @param roleType       角色类型
     * @param conditionType  发放条件
     * @return 是否成功
     */
    @Override
    public Boolean createAndGrantReward(Long inviteRecordId, MktPassengerInviteRewardConfigVo rewardConfigVo,
                                        Long passengerId, String roleType, String conditionType) {
        try {
            // 创建奖励记录
            MktPassengerInviteRewardBo rewardBo = new MktPassengerInviteRewardBo();
            rewardBo.setCampaignId(rewardConfigVo.getCampaignId());
            rewardBo.setInviteRecordId(inviteRecordId);
            rewardBo.setRewardConfigId(rewardConfigVo.getId());
            rewardBo.setPassengerId(passengerId);
            rewardBo.setRoleType(roleType);
            rewardBo.setConditionType(conditionType);
            rewardBo.setRewardType(rewardConfigVo.getRewardType());
            rewardBo.setRewardValue(rewardConfigVo.getRewardValue());
            rewardBo.setRewardMeta(rewardConfigVo.getRewardMeta());

            // 根据奖励类型进行不同的发放逻辑
            if (PassengerInviteRewardTypeEnum.CASH.getCode().equals(rewardConfigVo.getRewardType())) {
                // 现金奖励发放逻辑
                // : 调用钱包服务发放现金奖励
                rewardBo.setStatus(PassengerInviteRewardStatusEnum.GRANTED.getCode());
                log.info("发放现金奖励，乘客ID：{}，金额：{}", passengerId, rewardConfigVo.getRewardValue());
            } else if (PassengerInviteRewardTypeEnum.INTEGRAL.getCode().equals(rewardConfigVo.getRewardType())) {
                // 积分奖励发放逻辑
                // : 调用积分服务发放积分奖励
                rewardBo.setStatus(PassengerInviteRewardStatusEnum.GRANTED.getCode());
                log.info("发放积分奖励，乘客ID：{}，积分：{}", passengerId, rewardConfigVo.getRewardValue());
            } else if (PassengerInviteRewardTypeEnum.COUPON.getCode().equals(rewardConfigVo.getRewardType())) {
                // 优惠券奖励发放逻辑
                //目前只有一个
                Long couponId = rewardConfigVo.getRewardMeta().getCouponIds().get(0);
                CouponGrantToUserBo couponGrantToUserBo = mktCouponGrantService.grantCoupon(CouponGrantToUserBo.builder()
                        .couponId(couponId)
                        .userId(passengerId)
                        .grantCount(1)
                        .sourceId()
                        .sourceType(UserCouponSourceEnum.PASSENGER_INVITE.getCode())
                        .build());
                var statusEnum = couponGrantToUserBo.getResult().equals(CouponGrantToUserResultEnum.SUCCESS) ? PassengerInviteRewardStatusEnum.GRANTED : PassengerInviteRewardStatusEnum.GRANT_FAILED;
                rewardBo.setStatus(statusEnum.getCode());
                rewardBo.setRemark(couponGrantToUserBo.getResult().getMessage());
                //短信通知
                ThreadUtil.execAsync(() -> sendSms(passengerId, couponId, conditionType));
                log.info("发放优惠券奖励，乘客ID：{}，优惠券配置：{}", passengerId, rewardConfigVo.getRewardMeta());
            } else {
                log.warn("未知的奖励类型：{}", rewardConfigVo.getRewardType());
                rewardBo.setStatus(PassengerInviteRewardStatusEnum.GRANT_FAILED.getCode());
            }
            return insertByBo(rewardBo);
        } catch (Exception e) {
            log.error("创建并发放奖励失败，邀请记录ID：{}，乘客ID：{}", inviteRecordId, passengerId, e);
            return false;
        }
    }

    /**
     * 撤销奖励（客诉时使用）
     *
     * @param inviteRecordId 邀请记录ID
     * @return 是否成功
     */
    @Override
    public Boolean revokeRewards(Long inviteRecordId) {
        try {
            // 查询该邀请记录下的所有已发放奖励
            List<MktPassengerInviteRewardVo> rewards = queryByInviteRecordId(inviteRecordId);
            if (CollUtil.isEmpty(rewards)) {
                log.warn("查询到邀请记录下没有奖励，邀请记录ID：{}", inviteRecordId);
                return true;
            }

            boolean allSuccess = true;
            for (MktPassengerInviteRewardVo reward : rewards) {
                //首单奖励撤回
                if (PassengerInviteRewardStatusEnum.GRANTED.getCode().equals(reward.getStatus()) && PassengerInviteConditionTypeEnum.FIRST_ORDER.getCode().equals(reward.getConditionType())) {
                    // 根据奖励类型进行不同的撤销逻辑
                    boolean revokeSuccess = false;
                    if (PassengerInviteRewardTypeEnum.CASH.getCode().equals(reward.getRewardType())) {
                        // 现金奖励撤销逻辑
                        // 调用钱包服务撤销现金奖励
                        revokeSuccess = true;
                        log.info("撤销现金奖励，乘客ID：{}，金额：{}", reward.getPassengerId(), reward.getRewardValue());
                    } else if (PassengerInviteRewardTypeEnum.INTEGRAL.getCode().equals(reward.getRewardType())) {
                        // 积分奖励撤销逻辑
                        //调用积分服务撤销积分奖励
                        revokeSuccess = true;
                        log.info("撤销积分奖励，乘客ID：{}，积分：{}", reward.getPassengerId(), reward.getRewardValue());
                    } else if (PassengerInviteRewardTypeEnum.COUPON.getCode().equals(reward.getRewardType())) {
                        // 优惠券奖励撤销逻辑
                        log.info("撤销优惠券奖励，乘客ID：{}，优惠券配置：{}", reward.getPassengerId(), reward.getRewardMeta());
                        // 调用优惠券服务撤销优惠券
                        revokeSuccess = mktCouponGrantService.revokeCoupon(reward.getCouponGrantId());
                        log.debug("撤销优惠券奖励 couponGrantId {} {}", reward.getCouponGrantId(), revokeSuccess);
                    }
                    // 更新奖励状态
                    LambdaUpdateWrapper<MktPassengerInviteReward> updateWrapper = Wrappers.lambdaUpdate();
                    updateWrapper.eq(MktPassengerInviteReward::getId, reward.getId())
                            .set(MktPassengerInviteReward::getStatus,
                                    revokeSuccess ? PassengerInviteRewardStatusEnum.REFUND.getCode()
                                            : PassengerInviteRewardStatusEnum.REFUND_FAILED.getCode())
                            .set(MktPassengerInviteReward::getRefundRewardTime, new Date())
                            .set(MktPassengerInviteReward::getRemark, "客诉撤销奖励");

                    boolean updateSuccess = baseMapper.update(updateWrapper) > 0;
                    if (!updateSuccess || !revokeSuccess) {
                        allSuccess = false;
                    }
                }
            }
            return allSuccess;
        } catch (Exception e) {
            log.error("撤销奖励失败，邀请记录ID：{}", inviteRecordId, e);
            return false;
        }
    }

    /**
     * 根据邀请记录ID查询奖励列表
     *
     * @param inviteRecordId 邀请记录ID
     * @return 奖励列表
     */
    @Override
    public List<MktPassengerInviteRewardVo> queryByInviteRecordId(Long inviteRecordId) {
        LambdaQueryWrapper<MktPassengerInviteReward> lqw = Wrappers.lambdaQuery();
        lqw.eq(MktPassengerInviteReward::getInviteRecordId, inviteRecordId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * TODO R 发送短信
     * @param passengerId
     * @param couponId
     * @param conditionType
     */
    public void sendSms(Long passengerId, Long couponId, String conditionType) {
        PassengerInviteConditionTypeEnum conditionTypeEnum = PassengerInviteConditionTypeEnum.getByCodeThrow(conditionType);
        RemotePassengerVo passengerInfo = remotePassengerService.getPassengerInfo(passengerId);
        MktCoupon mktCoupon = mktCouponMapper.selectById(couponId);
        //邀请人获得好友注册奖励时发送短信：
        //【XXXXXX】您成功邀请好友注册，{$优惠券金额}元优惠券已到账！快到喜行出行小程序使用吧～
        //邀请人获得好友完单奖励时发送短信：
        //【XXXXXX】您的好友已完成首单，{$优惠券金额}元优惠券已到账！快到喜行出行小程序使用吧～
    }
}
