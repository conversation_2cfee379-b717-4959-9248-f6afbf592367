package com.feidi.xx.cross.market.service;

import cn.hutool.core.util.StrUtil;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.enums.market.PassengerInviteCampaignStatusEnum;
import com.feidi.xx.cross.market.domain.bo.PassengerInviteCampaignMbrQueryBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteCampaignVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteCampaignMapper;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 乘客邀请活动 乘客端业务
 */
@Slf4j
@Service
public class MktPassengerInviteCampaignMbrService {

    @Autowired
    private IMktPassengerInviteCodeService mktPassengerInviteCodeService;
    @Autowired
    private MktPassengerInviteCampaignMapper inviteCampaignMapper;
    @Autowired
    private IMktPassengerInviteRewardConfigService mktPassengerInviteRewardConfigService;

    /**
     * 根据城市code和邀请码查询活动
     *
     * @param queryBo 查询参数
     * @return 活动信息
     */
    public MktPassengerInviteCampaignVo queryByLocationAndInviteCode(PassengerInviteCampaignMbrQueryBo queryBo) {
        log.debug("queryByLocationAndInviteCode, queryBo: {}", queryBo);
        MktPassengerInviteCampaignVo inviteCampaignVo = queryByInviteCode(queryBo.getInviteCode());
        if (inviteCampaignVo == null) {
            inviteCampaignVo = queryByCityCode(queryBo.getCityCode());
        }
        if (inviteCampaignVo == null) {
            return null;
        }
        log.debug("queryByLocationAndInviteCode, inviteCampaignVo: {}", inviteCampaignVo);
        if (LoginHelper.isLogin() && StrUtil.isNotBlank(queryBo.getCityCode())) {
            //设置邀请码
            inviteCampaignVo.setInviteCode(mktPassengerInviteCodeService.createInviteCodeByUser(inviteCampaignVo.getId(), LoginHelper.getUserId(), queryBo.getCityCode()));
        }

        //查询奖励配置
        List<MktPassengerInviteRewardConfigVo> rewardConfigVos = mktPassengerInviteRewardConfigService.queryListByCampaignId(inviteCampaignVo.getId());
        inviteCampaignVo.setRewardConfigList(rewardConfigVos);
        return inviteCampaignVo;
    }

    public MktPassengerInviteCampaignVo queryByInviteCode(String inviteCode) {
        Long activityId = mktPassengerInviteCodeService.getActivityIdByInviteCode(inviteCode);
        if (activityId == null) {
            return null;
        }
        return inviteCampaignMapper.selectVoById(activityId);
    }

    private MktPassengerInviteCampaignVo queryByCityCode(String cityCode) {
        return inviteCampaignMapper.queryByCityCode(cityCode, PassengerInviteCampaignStatusEnum.ONGOING);
    }
}
