package com.feidi.xx.cross.market.service;

import cn.hutool.core.util.StrUtil;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.market.constants.MarketCacheConstants;
import com.feidi.xx.cross.common.enums.market.PassengerInviteCampaignStatusEnum;
import com.feidi.xx.cross.market.domain.bo.GetPassengerInviteCampaignShareBo;
import com.feidi.xx.cross.market.domain.bo.PassengerInviteCampaignMbrQueryBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteCampaignVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.domain.vo.ShareInfoVo;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteCampaignMapper;
import com.feidi.xx.resource.api.RemoteWxXcxApiService;
import com.feidi.xx.resource.api.domain.RemoteFile;
import com.feidi.xx.resource.api.domain.RemoteWxacodeParamBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.List;

/**
 * 乘客邀请活动 乘客端业务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MktPassengerInviteCampaignMbrService {

    private final IMktPassengerInviteCodeService mktPassengerInviteCodeService;
    private final MktPassengerInviteCampaignMapper inviteCampaignMapper;
    private final IMktPassengerInviteRewardConfigService mktPassengerInviteRewardConfigService;
    @DubboReference
    private final RemoteWxXcxApiService remoteWxXcxApiService;

    /**
     * 根据城市code和邀请码查询活动
     *
     * @param queryBo 查询参数
     * @return 活动信息
     */
    public MktPassengerInviteCampaignVo queryByLocationAndInviteCode(PassengerInviteCampaignMbrQueryBo queryBo) {
        log.debug("queryByLocationAndInviteCode, queryBo: {}", queryBo);
        MktPassengerInviteCampaignVo inviteCampaignVo = queryByInviteCode(queryBo.getInviteCode());
        if (inviteCampaignVo == null) {
            inviteCampaignVo = inviteCampaignMapper.queryByCityCode(queryBo.getCityCode(), PassengerInviteCampaignStatusEnum.ONGOING);
        }
        if (inviteCampaignVo == null) {
            return null;
        }
        log.debug("queryByLocationAndInviteCode, inviteCampaignVo: {}", inviteCampaignVo);
        //查询奖励配置
        List<MktPassengerInviteRewardConfigVo> rewardConfigVos = mktPassengerInviteRewardConfigService.queryListByCampaignId(inviteCampaignVo.getId());
        inviteCampaignVo.setRewardConfigList(rewardConfigVos);
        return inviteCampaignVo;
    }

    /**
     * 根据邀请码查询活动
     *
     * @param inviteCode
     * @return
     */
    public MktPassengerInviteCampaignVo queryByInviteCode(String inviteCode) {
        if (StrUtil.isBlank(inviteCode)) {
            return null;
        }
        Long activityId = mktPassengerInviteCodeService.getActivityIdByInviteCode(inviteCode);
        if (activityId == null) {
            return null;
        }
        return inviteCampaignMapper.selectVoById(activityId);
    }

    /**
     * 获取分享信息
     *
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ShareInfoVo getShareInfo(GetPassengerInviteCampaignShareBo bo) {
        log.debug("getShareInfo, bo: {}", bo);
        MktPassengerInviteCampaignVo inviteCampaignVo = inviteCampaignMapper.selectVoById(bo.getCampaignId());
        if (inviteCampaignVo == null) {
            return null;
        }

        String key = MarketCacheConstants.INVITE_SHARE_INFO_TEMP_KEY + bo.getKey();
        String str = RedisUtils.getCacheObject(key);
        if (str != null) {
            return JsonUtils.parseObject(str, ShareInfoVo.class);
        }

        //设置分享信息
        ShareInfoVo shareInfoVo = new ShareInfoVo();
        shareInfoVo.setShareTitle(inviteCampaignVo.getShareTitle());
        //设置邀请码
        String inviteCode = mktPassengerInviteCodeService.createInviteCodeByUser(bo.getCampaignId(), LoginHelper.getUserId(), bo.getCityCode());
        shareInfoVo.setInviteCode(inviteCode);
        //微信小程序码
        RemoteWxacodeParamBo remoteWxacodeParamBo = BeanUtils.copyProperties(bo, RemoteWxacodeParamBo.class);
        remoteWxacodeParamBo.setScene("mpic=" + inviteCode);
        shareInfoVo.setArg(remoteWxacodeParamBo.getScene());
        RemoteFile remoteFile = remoteWxXcxApiService.getwxacodeunlimit(remoteWxacodeParamBo);
        shareInfoVo.setQrcodeUrl(remoteFile.getUrl());

        //设置分享信息
        RedisUtils.setCacheObject(key, JsonUtils.toJsonString(shareInfoVo));
        //设置过期时间
        RedisUtils.expire(key, Duration.ofHours(8));
        return shareInfoVo;
    }
}
