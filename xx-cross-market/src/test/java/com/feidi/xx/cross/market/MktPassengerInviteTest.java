package com.feidi.xx.cross.market;

import com.feidi.xx.cross.market.mapper.MktPassengerInviteRecordMapper;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteRewardConfigMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class MktPassengerInviteTest {
    @Autowired
    private MktPassengerInviteRewardConfigMapper mktPassengerInviteRewardConfigMapper;
    @Autowired
    MktPassengerInviteRecordMapper mktPassengerInviteRecordMapper;

    @Test
    public void test() {
        System.out.println(mktPassengerInviteRewardConfigMapper.selectCountByCouponId(11L));
        System.out.println(mktPassengerInviteRewardConfigMapper.selectList());

    }
}
