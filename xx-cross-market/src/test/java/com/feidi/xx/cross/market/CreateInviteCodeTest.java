package com.feidi.xx.cross.market;

import com.feidi.xx.cross.market.service.IMktPassengerInviteCodeService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.stream.IntStream;

@SpringBootTest
public class CreateInviteCodeTest {

    @Autowired
    IMktPassengerInviteCodeService mktPassengerInviteCodeService;


    @Test
    public void genTest() {
        IntStream.range(1, 100).parallel()
                .forEach(value -> {
                    System.out.println(mktPassengerInviteCodeService.createInviteCodeByUser(1L, 4323L, "0001" + value));
                });
    }

}
