package com.feidi.xx.cross.operate.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.baomidou.mybatisplus.annotation.TableId;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.operate.domain.OprHoliday;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;

/**
 * 定价对象 Fin_holiday
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OprHoliday.class)
public class OprHolidayVo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 时间
     */
    private String date;

    /**
     * 是否为节假日
     */
    private String holiday;

    /**
     * 备注
     */
    private String remark;

}
