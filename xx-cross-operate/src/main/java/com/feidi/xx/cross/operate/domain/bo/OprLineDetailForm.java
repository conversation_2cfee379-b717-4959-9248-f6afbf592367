package com.feidi.xx.cross.operate.domain.bo;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 线路详情业务对象 opr_line_detail
 *
 * <AUTHOR>
 * @date 2024-04-26
 */
@Data
public class OprLineDetailForm {

    /**
     * 线路ID
     */
    @NotNull(message = "线路ID不能为空")
    private Long lineId;

    /**
     * 起点
     */
    @Valid
    @NotNull(message = "起点不能为空")
    private List<OprLineDetailBo> start;

    /**
     * 终点
     */
    @Valid
    @NotNull(message = "终点不能为空")
    private List<OprLineDetailBo> end;

}
