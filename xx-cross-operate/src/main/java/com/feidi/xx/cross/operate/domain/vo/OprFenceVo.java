package com.feidi.xx.cross.operate.domain.vo;

import com.feidi.xx.common.id2name.annotation.Id2Name;
import com.feidi.xx.cross.operate.domain.OprFence;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 电子围栏视图对象 opr_fence
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OprFence.class)
public class OprFenceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 省ID
     */
    @Id2Name(fullName = "province", index = "id")
    @ExcelProperty(value = "省ID")
    private Long provinceId;

    /**
     * 城市ID
     */
    @Id2Name(fullName = "city", index = "id")
    @ExcelProperty(value = "城市ID")
    private Long cityId;

    /**
     * 市编码
     */
    @ExcelProperty(value = "市编码")
    private String cityCode;

    /**
     * 区域编码
     */
    @ExcelProperty(value = "区域编码")
    private String adCode;

    /**
     * 围栏坐标点
     */
    @ExcelProperty(value = "围栏坐标点")
    private Double[] points;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 状态[StatusEnum]
     */
    @ExcelProperty(value = "状态[StatusEnum]")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 使用线路数量
     */
    @ExcelProperty(value = "使用线路数量")
    private Integer lineNum;

}
