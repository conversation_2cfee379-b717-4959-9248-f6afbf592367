package com.feidi.xx.cross.platform.api.hbk.domain.hbk.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 2.4.车主接单 (method: driver.receive.order) 参数
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
@Data
public class HbkVehicleInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车辆颜色
     */
    String color;

    /**
     * 车辆型号及名称 eg: "别克 微蓝6"，"几何汽车 A"
     */
    String modelName;

    /**
     * 车牌号
     */
    String plateNumber;

    /**
     * 车辆识别码（车架号）(用户投保)
     */
    String vin;

    /**
     * 发动机编号(用户投保)
     */
    String engineNumber;

}
