package com.feidi.xx.cross.platform.api.hbk.domain.hbk.model;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class MsgDetail implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String shortAddress;

    //消息类型为地图时所用到的经度
    private String lon;

    private String longAddress;

    //文本消息内容
    private String content;

    //消息类型为地图时所用到的纬度
    private String lat;

    private String tips;

    //语音url
    private String audioURL;

    private String fileName;

    private Boolean hasPlayed;

    private String conversationShowText;

    private String audioDuration;

    //发送语音消息时，这个必传，默认为1
    private Integer upLoadStatus;

}