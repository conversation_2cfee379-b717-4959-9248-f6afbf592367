package com.feidi.xx.cross.platform.api.hbk.domain.hbk.bo;

import com.feidi.xx.cross.platform.api.hbk.domain.hbk.model.Msg;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.UUID;

/**
 * 2.13 哈啰发送消息bo
 */
@Data
public class HbkMsgSendBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 乘客单，必传 platformNo
     */
    private String orderGuid;

    /**
     * 消息类型:1、文本,2、地图，7、已读,13、语音消息,其他编号为卡片消息，必传
     */
    private Integer msgType;

    /**
     * 业务线：1:顺风车；不传默认顺风车，必传
     */
    private Integer bizType;

    /**
     * 消息唯一ID，渠道生成一个唯一uuid即可
     */
    private String msgId;

    /**
     * 快捷消息类型
     * <p>
     * 1:表示快捷消息，必传
     */
    private Integer quickMsgType;

    /**
     * 场景类型(整个会话)，必传，1:普通接单前/接单后聊天场景
     */
    private Integer sceneType;

    /**
     * 内容，必传
     */
    private Msg content;

    /**
     * 司机手机号
     */
    private String mobilePhone;


    /**
     * 获取哈啰消息
     *
     * @param bo
     * @param readOnly 是否作为已读消息发送
     * @return
     */
    public static HbkMsgSendBo getInstance(RemoteHbkMsgSendBo bo, boolean readOnly) {
        HbkMsgSendBo convert = new HbkMsgSendBo();
        convert.setOrderGuid(bo.getPlatformNo());
        convert.setContent(bo.getContent());
        convert.setMsgId(UUID.randomUUID().toString());
        if (readOnly) {
            convert.setMsgType(7);
        } else {
            convert.setMsgType(bo.getMsgType());
        }
        // 以下固定
        convert.setQuickMsgType(1);
        convert.setSceneType(1);
        convert.setBizType(bo.getBizType());
        convert.setMobilePhone(bo.getMobilePhone());
        return convert;
    }

}
