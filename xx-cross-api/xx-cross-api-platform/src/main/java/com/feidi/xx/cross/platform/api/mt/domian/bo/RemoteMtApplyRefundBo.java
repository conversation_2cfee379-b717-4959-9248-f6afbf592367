package com.feidi.xx.cross.platform.api.mt.domian.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 退款通知 - 参数
 *
 * <AUTHOR>
 * @date 2024/9/11
 */
@Data
public class RemoteMtApplyRefundBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分配的合作方的标识，每个合作方一个，对应分配给合作方的client_id 必须
     */
    private String channel;

    /**
     * 请求时间，UnixTimestamp单位毫秒 必须
     */
    private Long timestamp;

    /**
     * 签名 必须
     */
    private String sign;

    /**
     * 美团订单号
     */
    private String mtOrderId;

    /**
     * 合作方订单号
     */
    private String partnerOrderId;

    /**
     * 取消原因
     */
    private String reason;

    /**
     * 美团手机号（虚拟号）
     */
    private String mtPhone;

    /**
     * 用于自定义虚拟号的绑定时长, 单位:分钟, 不传递则使用默认值30分钟
     */
    private Integer duration;

    /**
     * 美团消息ID
     */
    private String mtMsgId;

    /**
     * 消息类型：1文本消息，当前仅支持文本，其他类型正在建设中
     */
    private Integer msgType;

    /**
     * msgContent
     */
    private String msgContent;

    /**
     * 消息发送时间 (毫秒)
     */
    private Long createTime;

    /**
     * 唯一ID
     */
    private String outNo;

    /**
     * 退款金额 等于行程退款+附加退款 单位（分）
     */
    private Long refundPrice;

    /**
     * 行程退款金额 单位（分）
     */
    private Long tripPrice;

    /**
     * 附加费退款金额（高速费、停车费、桥路费、其他费用） 单位（分）
     */
    private Long additionalPrice;

    /**
     * 退款类型：部分退 PART、全额退 FULL
     */
    private String refundType;

    /**
     * 退款原因分类
     */
    private String reason2;

    /**
     * 退款原因详细描述
     */
    private String description;

    /**
     * 操作人Id
     */
    private String opUid;

    /**
     * 操作人名称
     */
    private String opName;

}
