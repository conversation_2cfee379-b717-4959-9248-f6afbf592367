package com.feidi.xx.cross.platform.api;


import com.feidi.xx.cross.platform.api.domain.RemotePlatformLogBo;
import com.feidi.xx.cross.platform.api.domain.RemotePlatformLogBo;

/**
 * 平台相关接口日志服务
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
public interface RemotePlatformLogService {

    /**
     * 保存平台操作日志
     *
     * @param remotePlatformLogBo
     */
    // 记录日志
    void saveLog(RemotePlatformLogBo remotePlatformLogBo);

}
