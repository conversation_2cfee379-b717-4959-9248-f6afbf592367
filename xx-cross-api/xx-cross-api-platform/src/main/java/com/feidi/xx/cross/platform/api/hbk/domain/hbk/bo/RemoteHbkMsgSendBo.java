package com.feidi.xx.cross.platform.api.hbk.domain.hbk.bo;

import com.feidi.xx.cross.platform.api.hbk.domain.hbk.model.Msg;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统调用bo
 */
@Data
public class RemoteHbkMsgSendBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 内容，必传
     */
    private Msg content;

    /**
     * 订单号
     */
    private String platformNo;

    /**
     * 消息类型:1、文本,2、地图，7、已读,13、语音消息,其他编号为卡片消息，必传
     */
    private Integer msgType;
    /**
     * 业务线：1:顺风车；不传默认顺风车，必传
     */
    private Integer bizType;
    /**
     * 消息唯一ID，渠道生成一个唯一uuid即可
     */
    private String msgId;

    /**
     * 司机手机号
     */
    private String mobilePhone;

}
