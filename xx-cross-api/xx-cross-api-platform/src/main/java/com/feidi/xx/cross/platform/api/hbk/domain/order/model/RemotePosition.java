package com.feidi.xx.cross.platform.api.hbk.domain.order.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 平台接口、车辆相关信息参数
 *
 * <AUTHOR>
 */
@Data
public class RemotePosition implements Serializable {

    /**
     * 经度 必须
     */
    String longitude;

    /**
     * 纬度 必须
     */
    String latitude;

    /**
     * 城市code 必须
     */
    String cityCode;

    /**
     * 城市名称 必须
     */
    String cityName;

    /**
     * 长地址 必须
     */
    String longAddress;

    /**
     * 短地址 必须
     */
    String shortAddress;

    /**
     * 城镇编码 必须
     */
    String adCode;

}
