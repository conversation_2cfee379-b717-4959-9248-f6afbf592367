package com.feidi.xx.cross.platform.api.domain;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 平台日志参数
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
@Data
@NoArgsConstructor
public class RemotePlatformLogBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台单号
     */
    private String platformNo;

    /**
     * 订单KEY
     */
    private String orderKey;

    /**
     * 接口名
     */
    private String apiName;

    /**
     * 接口编码
     */
    private String apiCode;

    /**
     * IP
     */
    private String ip;

    /**
     * 参数
     */
    private String paramsJson;

    /**
     * 结果
     */
    private String responseJson;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态码
     */
    private String statusCode;

    /**
     * 描述
     */
    private String statusMsg;

    /**
     * 时长
     */
    private Long duration;

    /**
     * 方向
     */
    private String direction;

    /**
     * 备注
     */
    private String remark;

    /**
     * 时间戳
     */
    private Long timeStamp;

}
