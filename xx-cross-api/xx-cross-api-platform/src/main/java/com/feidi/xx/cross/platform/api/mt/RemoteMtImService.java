package com.feidi.xx.cross.platform.api.mt;

import com.feidi.xx.cross.platform.api.hbk.domain.hbk.vo.RemotePlatformApiResponseVo;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtMsgSendBo;
import com.feidi.xx.cross.platform.api.hbk.domain.hbk.vo.RemotePlatformApiResponseVo;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtMsgReadBo;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtMsgSendBo;

public interface RemoteMtImService {

    /**
     * 司机发送消息
     * @param bo
     * @return
     */
    RemotePlatformApiResponseVo sendMsg(RemoteMtMsgSendBo bo);

    /**
     * 司机已读
     * @param bo
     * @return
     */
    RemotePlatformApiResponseVo readMsg(RemoteMtMsgReadBo bo);
}
