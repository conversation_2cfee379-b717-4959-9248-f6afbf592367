package com.feidi.xx.cross.platform.api.mt.domian.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 推送拼友信息 - 参数
 *
 * <AUTHOR>
 * @date 2024/9/11
 */
@Data
public class RemoteMtPushPoolFriendsInfoBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 分配的合作方的标识，每个合作方一个，对应分配给合作方的client_id 必须
     */
    private String channel;

    /**
     * 请求时间，UnixTimestamp单位毫秒 必须
     */
    private Long timestamp;

    /**
     * 签名 必须
     */
    private String sign;

    /**
     * 美团订单号
     */
    private String mtOrderId;

    /**
     * 合作方订单号
     */
    private String partnerOrderId;

    /**
     * 取消原因
     */
    private String reason;

    /**
     * 美团手机号（虚拟号）
     */
    private String mtPhone;

    /**
     * 用于自定义虚拟号的绑定时长, 单位:分钟, 不传递则使用默认值30分钟
     */
    private Integer duration;

    /**
     * 美团消息ID
     */
    private String mtMsgId;

    /**
     * 消息类型：1文本消息，当前仅支持文本，其他类型正在建设中
     */
    private Integer msgType;

    /**
     * msgContent
     */
    private String msgContent;

    /**
     * 消息发送时间 (毫秒)
     */
    private Long createTime;

    /**
     * 0-减少同行乘客，1-增加同行乘客
     */
    private Integer eventCode;

    /**
     * unix时间戳,ms
     */
    private Long eventTime;

    /**
     * 起点地址，如"北京市朝阳区望京东路4号"
     */
    private String startPointAddress;

    /**
     * 起点经度
     */
    private String startPointLng;

    /**
     * 起点纬度
     */
    private String startPointLat;

    /**
     * 起点名称，如"恒电大厦"
     */
    private String startPointName;

    /**
     * 终点地址，如"北京市丰台区莲花池东路118号"
     */
    private String endPointAddress;

    /**
     * 终点经度
     */
    private String endPointLng;

    /**
     * 终点纬度
     */
    private String endPointLat;

    /**
     * 终点名称，如"北京西站"
     */
    private String endPointName;

    /**
     * 订单状态，具体值参见订单状态定义,赋值为英文名称
     */
    private String status;

    /**
     * 真实手机号后4位
     */
    private String realPhoneSuffix;

    /**
     * 拼友出发时间，Unixtimestamp，单位毫秒
     */
    private Long departureTime;

    /**
     * 拼友人数
     */
    private Integer passengerNumber;

}
