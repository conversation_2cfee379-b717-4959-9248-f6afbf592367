package com.feidi.xx.cross.platform.api.hbk.domain.hbk.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 2.12 运力服务商检查是否可以发送消息（driver.check.sendMsg）
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HbkMsgCheckBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 乘客单
     */
    private String orderGuid;

    /**
     * 聊天场景类型,1:普通接单前/接单后聊天场景, 2:邀请场景
     */
    private Integer sceneType;

    /**
     * 司机手机号
     */
    private String mobilePhone;

    /**
     * 行程类型:1:顺风车；不传默认顺风车
     */
    private Integer journeyType;

    public HbkMsgCheckBo(String orderGuid) {
        this.orderGuid = orderGuid;
        this.sceneType = 1;
        this.journeyType = 1;
    }
}