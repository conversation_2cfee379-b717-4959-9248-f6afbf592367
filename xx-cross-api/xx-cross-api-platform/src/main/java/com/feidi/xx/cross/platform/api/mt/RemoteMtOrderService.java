package com.feidi.xx.cross.platform.api.mt;

import com.feidi.xx.cross.platform.api.hbk.domain.hbk.vo.RemotePlatformApiResponseVo;
import com.feidi.xx.cross.platform.api.mt.domian.bo.*;

/**
 * 美团平台订单相关接口
 *
 * <AUTHOR>
 * @date 2024/9/11
 */
public interface RemoteMtOrderService {

    /**
     * 推送订单状态
     *
     * @param pushStatusBo
     * @return
     */
    RemotePlatformApiResponseVo pushOrderStatus(RemoteMtPushStatusBo pushStatusBo);

    /**
     * 推送拼友信息
     *
     * @param pushPoolFriendsInfoBo
     * @return
     */
    RemotePlatformApiResponseVo pushPoolFriendsInfo(RemoteMtPushPoolFriendsInfoBo pushPoolFriendsInfoBo);

    /**
     * 获取乘客手机号
     *
     * @param queryPassengerPhoneBo
     * @return
     */
    RemotePlatformApiResponseVo queryPassengerPhone(RemoteMtQueryPassengerPhoneBo queryPassengerPhoneBo);

    /**
     * 根据乘客号码查询订单
     *
     * @param queryOrderByPhoneBo
     * @return
     */
    RemotePlatformApiResponseVo queryOrderByUserPhone(RemoteMtQueryOrderByPhoneBo queryOrderByPhoneBo);

    /**
     * 退款通知
     *
     * @param applyRefundBo
     * @return
     */
    RemotePlatformApiResponseVo applyRefund(RemoteMtApplyRefundBo applyRefundBo);

}
