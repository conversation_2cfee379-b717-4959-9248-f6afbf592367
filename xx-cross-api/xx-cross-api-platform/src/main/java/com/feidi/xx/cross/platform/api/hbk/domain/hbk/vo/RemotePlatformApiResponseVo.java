package com.feidi.xx.cross.platform.api.hbk.domain.hbk.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 外部平台接口响应信息主体（包含哈啰、美团等）
 *
 * <AUTHOR>
 * @date 2024/9/5
 */
@Data
@NoArgsConstructor
public class RemotePlatformApiResponseVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 状态
     */
    private String status;

    /**
     * 链路Id，排查问题时，需要提供请求时间和此id到哈啰侧
     */
    //private String traceId;

    /**
     * 平台响应的请求状态码
     */
    private Integer code;

    /**
     * 平台响应的请求状态描述
     */
    private String msg;

    /**
     * 业务响应的请求状态码，subCode对应下列接口中出参的code
     */
    private Integer subCode;

    /**
     * 业务响应的请求状态描述，subMsg对应下列接口中出参的msg
     */
    private String subMsg;

    /**
     * 业务响应的数据，对应下列接口中的data
     */
    private String data;

    /**
     * 哈啰平台所有的返回信息
     */
    private String response;

}
