package com.feidi.xx.cross.platform.api.hbk.domain.order.model;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 平台接口、车辆相关信息参数
 *
 * <AUTHOR>
 */
@Data
public class RemoteCar implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车辆颜色
     */
    String carColor;

    /**
     * 车辆品牌
     */
    String carBrand;

    /**
     * 车辆型号
     */
    private String carModel;

    /**
     * 车牌号
     */
    String carNumber;

    /**
     * 车辆识别码（车架号）(用户投保)
     */
    String vin;

    /**
     * 发动机编号(用户投保)
     */
    String engine;

}
