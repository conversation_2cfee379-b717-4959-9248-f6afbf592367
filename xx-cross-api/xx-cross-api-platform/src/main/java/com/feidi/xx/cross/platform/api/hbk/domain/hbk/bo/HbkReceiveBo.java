package com.feidi.xx.cross.platform.api.hbk.domain.hbk.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 2.4.车主接单 (method: driver.receive.order) 参数
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
@Data
public class HbkReceiveBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 哈啰订单号 (乘客单号)     必须
     */
    String orderNo;

    /**
     * 运力订单号，用于和哈啰乘客订单号一一关联 必须
     */
    String transportOrderNo;

    /**
     * 车主手机号         必须
     */
    String mobilePhone;

    /**
     * 车主身份证号，用于投保     必须
     */
    String certNo;

    /**
     * 车主性别(1-男2-女)       必须
     */
    String sex;

    /**
     * 车主姓名(用户投保)         必须
     */
    String name;

    //计划接乘客的时间，此时间必须介于
    //乘客最早出发时间(earliestDepartureTime) 和 乘客最晚出发时间(latestDepartureTime) 之间
    String planDepartureTime;

    /**
     * 车辆信息，部分用于乘客侧展示，部分用于投保    必须
     */
    HbkVehicleInfo vehicleInfo;

    /**
     * 车主当前位置，非必须
     */
    //HbkPosition position;

}
