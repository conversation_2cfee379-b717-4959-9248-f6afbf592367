package com.feidi.xx.cross.platform.api.hbk.domain.hbk.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * 2.9.取消订单 (method: driver.cancel.order) 参数
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
@Data
public class HbkCancelBo implements Serializable {

    /**
     * 哈啰订单号 (乘客单号)     必须
     */
    String orderNo;

    /**
     * 车主单号 必须
     */
    String driverOrderNo;

    /**
     * 车主手机号         必须
     */
    String mobilePhone;

    /**
     * 订单状态   必须
     */
    private Integer orderStatus = 20;

    /**
     * 取消原因    必须
     */
    private String cancelReason;

    /**
     * 取消类型，平台取消传10，司机取消传8   必须
     */
    private Integer cancelType = 10;
}
