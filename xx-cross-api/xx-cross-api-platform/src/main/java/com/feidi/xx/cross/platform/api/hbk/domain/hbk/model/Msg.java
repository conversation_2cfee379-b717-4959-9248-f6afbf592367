package com.feidi.xx.cross.platform.api.hbk.domain.hbk.model;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class Msg implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    //业务线
    private Integer bizType;

    //消息的详细内容，MsgDetail类型就是下面的类
    private MsgDetail data;

    private String msgId;

    /**
     * 消息类型
     */
    private Integer type;

    private Integer localState;

    private String passengerOrderGuid;

    private String driverOrderGuid;

    private Boolean ignore;

    private String fromNickname;

    private String toUserGuid;

    //时间戳，必传
    private Long ts;

}