package com.feidi.xx.cross.platform.api.hbk.domain.hbk.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 哈啰位置参数
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
@Data
public class HbkPosition implements Serializable {

    /**
     * 经度 必须
     */
    String lon;

    /**
     * 纬度 必须
     */
    String lat;

    /**
     * 城市名称 必须
     */
    String cityName;

    /**
     * 长地址 必须
     */
    String longAddr;

    /**
     * 短地址 必须
     */
    String shortAddr;

    /**
     * 城市code 必须，火星坐标系
     */
    String cityCode;

    /**
     * 城镇编码 必须，火星坐标系
     */
    String adCode;

}
