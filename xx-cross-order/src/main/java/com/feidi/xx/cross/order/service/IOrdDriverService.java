package com.feidi.xx.cross.order.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.order.domain.OrdDriver;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.bo.OrdDriverBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderDispatchBo;
import com.feidi.xx.cross.order.domain.vo.OrdDriverVo;

import java.util.Collection;
import java.util.List;

/**
 * 订单司机Service接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface IOrdDriverService {

    /**
     * 查询订单司机
     *
     * @param id 主键
     * @return 订单司机
     */
    OrdDriverVo queryById(Long id);

    /**
     * 分页查询订单司机列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单司机分页列表
     */
    TableDataInfo<OrdDriverVo> queryPageList(OrdDriverBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的订单司机列表
     *
     * @param bo 查询条件
     * @return 订单司机列表
     */
    List<OrdDriverVo> queryList(OrdDriverBo bo);

    /**
     * 新增订单司机
     *
     * @param bo 订单司机
     * @return 是否新增成功
     */
    Boolean insertByBo(OrdDriverBo bo);

    /**
     * 修改订单司机
     *
     * @param bo 订单司机
     * @return 是否修改成功
     */
    Boolean updateByBo(OrdDriverBo bo);

    /**
     * 校验并批量删除订单司机信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据订单ID批量查询订单司机
     *
     * @param orderIds 订单ID集合
     * @return 订单司机
     */
    List<OrdDriverVo> queryByOrderIds(List<Long> orderIds);

    /**
     * 根据订单ID和调度司机ID查询当前调度司机最新的订单司机信息
     *
     * @param orderId          订单ID
     * @param dispatchDriverId 调度司机ID
     * @return 订单司机信息
     */
    OrdDriverVo queryLatestByOrderIdAndDispatchDriverId(Long orderId, Long dispatchDriverId);

    /**
     * 订单绑定司机
     *
     * @param order    订单信息
     * @param dispatchBo 调度参数
     * @return OtDriverBo 订单司机信息
     */
    OrdDriver bindDriver(OrdOrder order, OrdOrderDispatchBo dispatchBo);
}
